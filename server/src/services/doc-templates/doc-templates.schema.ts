// TypeBox schema for doc-templates service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

// Plan class enum from original schema
const PlanClassSchema = Type.Union([
  Type.Literal('core'), Type.Literal('125'), Type.Literal('105'), Type.Literal('misc')
])

// Sections schema for document templates (simplified version)
const SectionsSchema = Type.Object({
  title: Type.Optional(Type.String()),
  content: Type.Optional(Type.String()),
  order: Type.Optional(Type.Number()),
  required: Type.Optional(Type.Boolean())
}, { additionalProperties: true })

export const docTemplatesSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Required fields from original schema
  name: Type.String(), // Required
  sections: SectionsSchema, // Required

  // Document template configuration
  description: Type.Optional(Type.String()),
  class: Type.Optional(PlanClassSchema),
  subClass: Type.Optional(Type.String()),
  smb: Type.Optional(Type.Boolean()),

  // Common fields
  ...commonFields.properties
}, { additionalProperties: false })

export type DocTemplates = Static<typeof docTemplatesSchema>
export const docTemplatesValidator = getValidator(docTemplatesSchema, dataValidator)
export const docTemplatesResolver = resolve<DocTemplates, HookContext>({})
export const docTemplatesExternalResolver = resolve<DocTemplates, HookContext>({})

export const docTemplatesDataSchema = Type.Object({
  // Required fields for creating new doc templates (no required fields per original)
  ...Type.Partial(Type.Omit(docTemplatesSchema, ['_id'])).properties
}, { additionalProperties: false })

export type DocTemplatesData = Static<typeof docTemplatesDataSchema>
export const docTemplatesDataValidator = getValidator(docTemplatesDataSchema, dataValidator)
export const docTemplatesDataResolver = resolve<DocTemplatesData, HookContext>({})

export const docTemplatesPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(docTemplatesSchema, ['_id'])).properties,

  // MongoDB operators
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any()))
}, { additionalProperties: false })
export type DocTemplatesPatch = Static<typeof docTemplatesPatchSchema>
export const docTemplatesPatchValidator = getValidator(docTemplatesPatchSchema, dataValidator)
export const docTemplatesPatchResolver = resolve<DocTemplatesPatch, HookContext>({})

// Schema for allowed query properties - matching original schema
export const docTemplatesQuerySchema = Type.Object({
  ...querySyntax(docTemplatesSchema).properties
}, { additionalProperties: false })

export type DocTemplatesQuery = Static<typeof docTemplatesQuerySchema>
export const docTemplatesQueryValidator = getValidator(docTemplatesQuerySchema, queryValidator)
export const docTemplatesQueryResolver = resolve<DocTemplatesQuery, HookContext>({})
