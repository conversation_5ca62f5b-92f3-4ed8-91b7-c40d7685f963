// TypeBox schema for teams service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, ImageSchema, PhoneSchema, addToSet, pull } from '../../utils/common/typebox-schemas.js'

export const teamsSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Support team fields from original schema
  name: Type.Optional(Type.String()),
  types: Type.Optional(Type.Array(Type.Union([
    Type.Literal('sales'), Type.Literal('support')
  ]))),
  avatar: Type.Optional(ImageSchema),
  refs: Type.Optional(Type.Array(ObjectIdSchema())),
  invited: Type.Optional(Type.Array(ObjectIdSchema())),
  req: Type.Optional(Type.Array(ObjectIdSchema())),
  contract: Type.Optional(ObjectIdSchema()),
  calendar: Type.Optional(ObjectIdSchema()),
  phone: Type.Optional(PhoneSchema),
  sms: Type.Optional(PhoneSchema),
  email: Type.Optional(Type.String()),
  priority: Type.Optional(Type.Array(ObjectIdSchema())),
  online: Type.Optional(Type.Array(ObjectIdSchema())),

  // Common fields
  ...commonFields.properties
}, { additionalProperties: false })

export type Teams = Static<typeof teamsSchema>
export const teamsValidator = getValidator(teamsSchema, dataValidator)
export const teamsResolver = resolve<Teams, HookContext>({})
export const teamsExternalResolver = resolve<Teams, HookContext>({})

export const teamsDataSchema = Type.Object({
  // No required fields for creation per original schema
  ...Type.Partial(Type.Omit(teamsSchema, ['_id'])).properties
}, { additionalProperties: false })

export type TeamsData = Static<typeof teamsDataSchema>
export const teamsDataValidator = getValidator(teamsDataSchema, dataValidator)
export const teamsDataResolver = resolve<TeamsData, HookContext>({})

export const teamsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(teamsSchema, ['_id'])).properties,

  // MongoDB operators - matching original pushPull for refs
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $addToSet: Type.Optional(addToSet([
    { path: 'refs', type: ObjectIdSchema() },
    { path: 'invited', type: ObjectIdSchema() },
    { path: 'req', type: ObjectIdSchema() },
    { path: 'priority', type: ObjectIdSchema() },
    { path: 'online', type: ObjectIdSchema() }
  ])),
  $pull: Type.Optional(pull([
    { path: 'refs', type: ObjectIdSchema() },
    { path: 'invited', type: ObjectIdSchema() },
    { path: 'req', type: ObjectIdSchema() },
    { path: 'priority', type: ObjectIdSchema() },
    { path: 'online', type: ObjectIdSchema() }
  ]))
}, { additionalProperties: false })
export type TeamsPatch = Static<typeof teamsPatchSchema>
export const teamsPatchValidator = getValidator(teamsPatchSchema, dataValidator)
export const teamsPatchResolver = resolve<TeamsPatch, HookContext>({})

// Schema for allowed query properties - matching original schema
export const teamsQuerySchema = Type.Object({
  ...querySyntax(teamsSchema).properties
}, { additionalProperties: false })

export type TeamsQuery = Static<typeof teamsQuerySchema>
export const teamsQueryValidator = getValidator(teamsQuerySchema, queryValidator)
export const teamsQueryResolver = resolve<TeamsQuery, HookContext>({})
