// TypeBox schema for funds service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

// Status updates schema for nominee status changes
const StatusUpdateSchema = Type.Object({
  from: Type.Optional(Type.Number()),
  to: Type.Optional(Type.Number()),
  at: Type.Optional(Type.Any()),
  by: Type.Optional(ObjectIdSchema())
}, { additionalProperties: false })

export const fundsSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Required fields from original schema
  name: Type.String(), // Required
  state: Type.String(), // Required

  // Fund governance fields from original schema
  board: Type.Optional(Type.Array(ObjectIdSchema())),

  // CRITICAL Pattern Property - This was completely lost!
  nominees: Type.Optional(Type.Record(Type.String(), Type.Object({
    id: Type.Optional(ObjectIdSchema()),
    status: Type.Optional(Type.Union([
      Type.Literal(0), Type.Literal(1), Type.Literal(2),
      Type.Literal(3), Type.Literal(4), Type.Literal(5)
    ])), // 0=invited, 1=accepted, 2=active, 3=rejected, 4=inactive, 5=removed
    statusUpdates: Type.Optional(Type.Array(StatusUpdateSchema))
  }))), // Pattern: ^.*$

  // Common fields
  ...commonFields.properties
}, { additionalProperties: false })

export type Funds = Static<typeof fundsSchema>
export const fundsValidator = getValidator(fundsSchema, dataValidator)
export const fundsResolver = resolve<Funds, HookContext>({})
export const fundsExternalResolver = resolve<Funds, HookContext>({})

export const fundsDataSchema = Type.Object({
  // Required fields for creating new funds
  name: Type.String(),
  state: Type.String(),

  // All other fields from main schema (optional for creation)
  ...Type.Partial(Type.Omit(fundsSchema, ['_id', 'name', 'state'])).properties
}, { additionalProperties: false })

export type FundsData = Static<typeof fundsDataSchema>
export const fundsDataValidator = getValidator(fundsDataSchema, dataValidator)
export const fundsDataResolver = resolve<FundsData, HookContext>({})

export const fundsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(fundsSchema, ['_id'])).properties,

  // MongoDB operators
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any()))
}, { additionalProperties: false })
export type FundsPatch = Static<typeof fundsPatchSchema>
export const fundsPatchValidator = getValidator(fundsPatchSchema, dataValidator)
export const fundsPatchResolver = resolve<FundsPatch, HookContext>({})

// Schema for allowed query properties - matching original schema
export const fundsQuerySchema = Type.Object({
  ...querySyntax(fundsSchema).properties
}, { additionalProperties: false })

export type FundsQuery = Static<typeof fundsQuerySchema>
export const fundsQueryValidator = getValidator(fundsQuerySchema, queryValidator)
export const fundsQueryResolver = resolve<FundsQuery, HookContext>({})
