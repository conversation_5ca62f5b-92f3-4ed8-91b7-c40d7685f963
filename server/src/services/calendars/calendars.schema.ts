// TypeBox schema for calendars service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

// Notifications schema for calendar notifications
const NotificationsSchema = Type.Object({
  email: Type.Optional(Type.Boolean()),
  sms: Type.Optional(Type.Boolean()),
  push: Type.Optional(Type.Boolean()),
  beforeMinutes: Type.Optional(Type.Number())
}, { additionalProperties: false })

// Schedule schema for calendar scheduling
const ScheduleSchema = Type.Object({
  timezone: Type.Optional(Type.String()),
  workingHours: Type.Optional(Type.Object({
    start: Type.Optional(Type.String()),
    end: Type.Optional(Type.String()),
    days: Type.Optional(Type.Array(Type.Number()))
  })),
  availability: Type.Optional(Type.Array(Type.Object({
    start: Type.Optional(Type.Any()),
    end: Type.Optional(Type.Any()),
    available: Type.Optional(Type.Boolean())
  })))
}, { additionalProperties: false })

export const calendarsSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Calendar configuration
  name: Type.Optional(Type.String()),
  ownerDefault: Type.Optional(Type.Boolean()),
  owner: Type.Optional(ObjectIdSchema()),
  ownerService: Type.Optional(Type.String()),

  // Arrays of related entities
  editors: Type.Optional(Type.Array(ObjectIdSchema())),
  archived: Type.Optional(Type.Array(ObjectIdSchema())),
  past: Type.Optional(Type.Array(ObjectIdSchema())),
  future: Type.Optional(Type.Array(ObjectIdSchema())),

  // Complex nested objects
  notify: Type.Optional(NotificationsSchema),
  schedule: Type.Optional(ScheduleSchema),
  tokens: Type.Optional(Type.Object({
    google: Type.Optional(Type.String())
  })),

  // Common fields
  ...commonFields.properties
}, { additionalProperties: false })

export type Calendars = Static<typeof calendarsSchema>
export const calendarsValidator = getValidator(calendarsSchema, dataValidator)
export const calendarsResolver = resolve<Calendars, HookContext>({})
export const calendarsExternalResolver = resolve<Calendars, HookContext>({})

export const calendarsDataSchema = Type.Object({
  ...Type.Omit(calendarsSchema, ['_id']).properties
}, { additionalProperties: false })

export type CalendarsData = Static<typeof calendarsDataSchema>
export const calendarsDataValidator = getValidator(calendarsDataSchema, dataValidator)
export const calendarsDataResolver = resolve<CalendarsData, HookContext>({})

export const calendarsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(calendarsSchema, ['_id'])).properties,

  // MongoDB operators
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any()))
}, { additionalProperties: false })
export type CalendarsPatch = Static<typeof calendarsPatchSchema>
export const calendarsPatchValidator = getValidator(calendarsPatchSchema, dataValidator)
export const calendarsPatchResolver = resolve<CalendarsPatch, HookContext>({})

// Schema for allowed query properties - matching original schema
export const calendarsQuerySchema = Type.Object({
  ...querySyntax(calendarsSchema).properties
}, { additionalProperties: false })

export type CalendarsQuery = Static<typeof calendarsQuerySchema>
export const calendarsQueryValidator = getValidator(calendarsQuerySchema, queryValidator)
export const calendarsQueryResolver = resolve<CalendarsQuery, HookContext>({})
