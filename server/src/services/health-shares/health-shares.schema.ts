// TypeBox schema for health-shares service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, ImageSchema, VideoSchema } from '../../utils/common/typebox-schemas.js'

// Vector store schema for health share products
const VectorStoreSchema = Type.Object({
  id: Type.Optional(Type.String()),
  fileIds: Type.Optional(Type.Array(Type.String())),
  updatedAt: Type.Optional(Type.Any())
}, { additionalProperties: false })

export const healthSharesSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Required fields from original schema
  name: Type.String(), // Required

  // Health share fields from original schema
  description: Type.Optional(Type.String()),
  aka: Type.Optional(Type.Array(Type.String())),
  logo: Type.Optional(ImageSchema),
  cc_video: Type.Optional(VideoSchema),
  video: Type.Optional(VideoSchema),

  // CRITICAL Pattern Properties - All 3 were completely lost!
  files: Type.Optional(Type.Record(Type.String(), ImageSchema)), // Pattern: ^.*$

  products: Type.Optional(Type.Record(Type.String(), Type.Object({
    id: Type.Optional(Type.String()),
    name: Type.Optional(Type.String()),
    guidelines: Type.Optional(ImageSchema),
    vectorStore: Type.Optional(VectorStoreSchema)
  }))), // Pattern: ^.*$

  financials: Type.Optional(Type.Record(Type.String({ pattern: '^\\d{4}$' }), Type.Object({
    // Financial data by year (key is the year)
    total_revenue: Type.Optional(Type.Number()),
    sharing_expense: Type.Optional(Type.Number()),
    admin_expense: Type.Optional(Type.Number()),
    net_assets_start: Type.Optional(Type.Number()),
    net_assets: Type.Optional(Type.Number()),
    cash_on_hand: Type.Optional(Type.Number()),
    highest_paid_executive: Type.Optional(Type.Number())
  }))), // Pattern: ^\d{4}$

  // Common fields
  ...commonFields.properties
}, { additionalProperties: false })

export type HealthShares = Static<typeof healthSharesSchema>
export const healthSharesValidator = getValidator(healthSharesSchema, dataValidator)
export const healthSharesResolver = resolve<HealthShares, HookContext>({})
export const healthSharesExternalResolver = resolve<HealthShares, HookContext>({})

export const healthSharesDataSchema = Type.Object({
  // Required fields for creating new health shares
  name: Type.String(),

  // All other fields from main schema (optional for creation)
  ...Type.Partial(Type.Omit(healthSharesSchema, ['_id', 'name'])).properties
}, { additionalProperties: false })

export type HealthSharesData = Static<typeof healthSharesDataSchema>
export const healthSharesDataValidator = getValidator(healthSharesDataSchema, dataValidator)
export const healthSharesDataResolver = resolve<HealthSharesData, HookContext>({})

export const healthSharesPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(healthSharesSchema, ['_id'])).properties,

  // MongoDB operators
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any()))
}, { additionalProperties: false })
export type HealthSharesPatch = Static<typeof healthSharesPatchSchema>
export const healthSharesPatchValidator = getValidator(healthSharesPatchSchema, dataValidator)
export const healthSharesPatchResolver = resolve<HealthSharesPatch, HookContext>({})

// Schema for allowed query properties - matching original schema with custom name/aka queries
export const healthSharesQuerySchema = Type.Object({
  ...querySyntax(healthSharesSchema).properties,
  // Custom query properties from original
  name: Type.Optional(Type.Any()),
  aka: Type.Optional(Type.Any())
}, { additionalProperties: false })

export type HealthSharesQuery = Static<typeof healthSharesQuerySchema>
export const healthSharesQueryValidator = getValidator(healthSharesQuerySchema, queryValidator)
export const healthSharesQueryResolver = resolve<HealthSharesQuery, HookContext>({})
