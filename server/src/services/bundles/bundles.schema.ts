// TypeBox schema for bundles service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, addToSet, pull } from '../../utils/common/typebox-schemas.js'

export const bundlesSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Required fields from original schema
  name: Type.String(), // Required
  provider: ObjectIdSchema(), // Required

  // Bundle configuration
  public: Type.Optional(Type.Boolean()),
  description: Type.Optional(Type.String()),
  price: Type.Optional(Type.Number()),

  // Arrays of related entities - matching original schema exactly
  plans: Type.Optional(Type.Array(ObjectIdSchema())),
  locations: Type.Optional(Type.Array(Type.String())), // stringified lng|lat from provider locations
  prices: Type.Optional(Type.Array(ObjectIdSchema())),
  cats: Type.Optional(Type.Array(ObjectIdSchema())),
  networks: Type.Optional(Type.Array(ObjectIdSchema())),
  managers: Type.Optional(Type.Array(ObjectIdSchema())),
  writers: Type.Optional(Type.Array(ObjectIdSchema())),

  // Common fields
  ...commonFields.properties
}, { additionalProperties: false })

export type Bundles = Static<typeof bundlesSchema>
export const bundlesValidator = getValidator(bundlesSchema, dataValidator)
export const bundlesResolver = resolve<Bundles, HookContext>({})
export const bundlesExternalResolver = resolve<Bundles, HookContext>({})

export const bundlesDataSchema = Type.Object({
  // Required fields for creating new bundles
  name: Type.String(),
  provider: ObjectIdSchema(),

  // All other fields from main schema (optional for creation)
  ...Type.Partial(Type.Omit(bundlesSchema, ['_id', 'name', 'provider'])).properties
}, { additionalProperties: false })

export type BundlesData = Static<typeof bundlesDataSchema>
export const bundlesDataValidator = getValidator(bundlesDataSchema, dataValidator)
export const bundlesDataResolver = resolve<BundlesData, HookContext>({})

export const bundlesPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(bundlesSchema, ['_id'])).properties,

  // MongoDB operators - matching original pushPull configuration
  $addToSet: Type.Optional(addToSet([
    { path: 'prices', type: ObjectIdSchema() },
    { path: 'networks', type: ObjectIdSchema() },
    { path: 'managers', type: ObjectIdSchema() },
    { path: 'writers', type: ObjectIdSchema() },
    { path: 'cats', type: ObjectIdSchema() },
    { path: 'plans', type: ObjectIdSchema() }
  ])),
  $pull: Type.Optional(pull([
    { path: 'prices', type: ObjectIdSchema() },
    { path: 'networks', type: ObjectIdSchema() },
    { path: 'managers', type: ObjectIdSchema() },
    { path: 'writers', type: ObjectIdSchema() },
    { path: 'cats', type: ObjectIdSchema() },
    { path: 'plans', type: ObjectIdSchema() }
  ])),

  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any()))
}, { additionalProperties: false })
export type BundlesPatch = Static<typeof bundlesPatchSchema>
export const bundlesPatchValidator = getValidator(bundlesPatchSchema, dataValidator)
export const bundlesPatchResolver = resolve<BundlesPatch, HookContext>({})

// Schema for allowed query properties - matching original schema
export const bundlesQuerySchema = Type.Object({
  ...querySyntax(bundlesSchema).properties,

  // Custom query properties from original schema
  name: Type.Optional(Type.Any())
}, { additionalProperties: false })

export type BundlesQuery = Static<typeof bundlesQuerySchema>
export const bundlesQueryValidator = getValidator(bundlesQuerySchema, queryValidator)
export const bundlesQueryResolver = resolve<BundlesQuery, HookContext>({})
