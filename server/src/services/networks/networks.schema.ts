// TypeBox schema for networks service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, ImageSchema, addToSet, pull } from '../../utils/common/typebox-schemas.js'

export const networksSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Required fields from original schema
  name: Type.String(), // Required
  description: Type.String(), // Required

  // Network collaboration fields from original schema
  avatar: Type.Optional(ImageSchema),
  access: Type.Optional(Type.Union([
    Type.Literal('public'), Type.Literal('private'), Type.Literal('searchable')
  ])),
  subs: Type.Optional(Type.Array(ObjectIdSchema())),
  managers: Type.Optional(Type.Array(ObjectIdSchema())),
  writers: Type.Optional(Type.Array(ObjectIdSchema())),
  images: Type.Optional(Type.Array(ImageSchema)),
  lastSync: Type.Optional(Type.String()),
  bundle_changes: Type.Optional(Type.Array(ObjectIdSchema())),
  plans: Type.Optional(Type.Array(ObjectIdSchema())), // added by network, may not match on plan
  bundles: Type.Optional(Type.Array(ObjectIdSchema())), // added by network - may not be matching on bundle
  bundle_reqs: Type.Optional(Type.Array(ObjectIdSchema())),
  plan_reqs: Type.Optional(Type.Array(ObjectIdSchema())),
  bundle_invites: Type.Optional(Type.Array(ObjectIdSchema())),
  plans_invites: Type.Optional(Type.Array(ObjectIdSchema())),

  // Common fields
  ...commonFields.properties
}, { additionalProperties: false })

export type Networks = Static<typeof networksSchema>
export const networksValidator = getValidator(networksSchema, dataValidator)
export const networksResolver = resolve<Networks, HookContext>({})
export const networksExternalResolver = resolve<Networks, HookContext>({})

export const networksDataSchema = Type.Object({
  // Required fields for creating new networks
  name: Type.String(),
  description: Type.String(),

  // All other fields from main schema (optional for creation)
  ...Type.Partial(Type.Omit(networksSchema, ['_id', 'name', 'description'])).properties
}, { additionalProperties: false })

export type NetworksData = Static<typeof networksDataSchema>
export const networksDataValidator = getValidator(networksDataSchema, dataValidator)
export const networksDataResolver = resolve<NetworksData, HookContext>({
  // Default access resolver from original schema
  access: async (val) => {
    if (!val) return 'private'
    return val
  }
})

export const networksPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(networksSchema, ['_id'])).properties,

  // MongoDB operators - matching original pushPull configuration
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $addToSet: Type.Optional(addToSet([
    { path: 'subs', type: ObjectIdSchema() },
    { path: 'managers', type: ObjectIdSchema() },
    { path: 'writers', type: ObjectIdSchema() },
    { path: 'images', type: ImageSchema },
    { path: 'bundle_changes', type: ObjectIdSchema() },
    { path: 'plans', type: ObjectIdSchema() },
    { path: 'bundles', type: ObjectIdSchema() },
    { path: 'bundle_reqs', type: ObjectIdSchema() },
    { path: 'plan_reqs', type: ObjectIdSchema() },
    { path: 'bundle_invites', type: ObjectIdSchema() },
    { path: 'plans_invites', type: ObjectIdSchema() }
  ])),
  $pull: Type.Optional(pull([
    { path: 'subs', type: ObjectIdSchema() },
    { path: 'managers', type: ObjectIdSchema() },
    { path: 'writers', type: ObjectIdSchema() },
    { path: 'images', type: ImageSchema },
    { path: 'bundle_changes', type: ObjectIdSchema() },
    { path: 'plans', type: ObjectIdSchema() },
    { path: 'bundles', type: ObjectIdSchema() },
    { path: 'bundle_reqs', type: ObjectIdSchema() },
    { path: 'plan_reqs', type: ObjectIdSchema() },
    { path: 'bundle_invites', type: ObjectIdSchema() },
    { path: 'plans_invites', type: ObjectIdSchema() }
  ]))
}, { additionalProperties: false })
export type NetworksPatch = Static<typeof networksPatchSchema>
export const networksPatchValidator = getValidator(networksPatchSchema, dataValidator)
export const networksPatchResolver = resolve<NetworksPatch, HookContext>({})

// Schema for allowed query properties - matching original schema
export const networksQuerySchema = Type.Object({
  ...querySyntax(networksSchema).properties
}, { additionalProperties: false })

export type NetworksQuery = Static<typeof networksQuerySchema>
export const networksQueryValidator = getValidator(networksQuerySchema, queryValidator)
export const networksQueryResolver = resolve<NetworksQuery, HookContext>({})
