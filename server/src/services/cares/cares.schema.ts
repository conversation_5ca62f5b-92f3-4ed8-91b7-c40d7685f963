// TypeBox schema for cares service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, ImageSchema, addToSet, pull } from '../../utils/common/typebox-schemas.js'

// Exes schema for conditions
const ExesSchema = Type.Object({
  id: Type.Optional(ObjectIdSchema()),
  name: Type.Optional(Type.String()),
  code: Type.Optional(Type.String())
}, { additionalProperties: false })

// Paids schema for payment tracking
const PaidsSchema = Type.Object({
  paid: Type.Optional(Type.Number()),
  paidAt: Type.Optional(Type.Any()),
  paidBy: Type.Optional(ObjectIdSchema()),
  paidMethod: Type.Optional(Type.String())
}, { additionalProperties: false })

export const caresSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Required fields from original schema
  person: ObjectIdSchema(), // Required
  plan: ObjectIdSchema(), // Required
  patient: ObjectIdSchema(), // Required

  // Care management fields
  org: Type.Optional(ObjectIdSchema()),
  status: Type.Optional(Type.Union([
    Type.Literal(0), Type.Literal(1), Type.Literal(2),
    Type.Literal(3), Type.Literal(4), Type.Literal(5)
  ])),
  planPriority: Type.Optional(Type.Union([
    Type.Literal(0), Type.Literal(1), Type.Literal(2),
    Type.Literal(3), Type.Literal(4), Type.Literal(5)
  ])),
  patientPriority: Type.Optional(Type.Union([
    Type.Literal(0), Type.Literal(1), Type.Literal(2),
    Type.Literal(3), Type.Literal(4), Type.Literal(5)
  ])),
  providerPriority: Type.Optional(Type.Union([
    Type.Literal(0), Type.Literal(1), Type.Literal(2),
    Type.Literal(3), Type.Literal(4), Type.Literal(5)
  ])),

  // Dates and timeline
  initDate: Type.Optional(Type.Any()),
  name: Type.Optional(Type.String()),
  targetDate: Type.Optional(Type.Any()),
  lastVisit: Type.Optional(Type.Any()),

  // Arrays of related entities
  visits: Type.Optional(Type.Array(ObjectIdSchema())),
  providers: Type.Optional(Type.Array(ObjectIdSchema())),
  practitioners: Type.Optional(Type.Array(ObjectIdSchema())),
  children: Type.Optional(Type.Array(ObjectIdSchema())),
  related: Type.Optional(Type.Array(ObjectIdSchema())),
  conditions: Type.Optional(Type.Array(ExesSchema)),
  threads: Type.Optional(Type.Array(ObjectIdSchema())),

  // Care details
  parent: Type.Optional(ObjectIdSchema()),
  preventive: Type.Optional(Type.Boolean()),

  // Financial tracking
  total: Type.Optional(Type.Number()),
  subtotal: Type.Optional(Type.Number()),
  balance: Type.Optional(Type.Number()),
  balanceSyncedAt: Type.Optional(Type.Any()),
  ...PaidsSchema.properties,

  // CRITICAL Pattern Property - This was completely lost!
  files: Type.Optional(Type.Record(Type.String(), ImageSchema)), // Pattern: ^.*$

  // Common fields
  ...commonFields.properties
}, { additionalProperties: false })

export type Cares = Static<typeof caresSchema>
export const caresValidator = getValidator(caresSchema, dataValidator)
export const caresResolver = resolve<Cares, HookContext>({
  // Resolvers from original schema
  status: async (val) => {
    if (!val && val !== 0) return 0
    return val
  },
  patientPriority: async (val) => {
    if (!val && val !== 0) return 0
    return val
  }
})
export const caresExternalResolver = resolve<Cares, HookContext>({})

export const caresDataSchema = Type.Object({
  // Required fields for creating new cares
  person: ObjectIdSchema(),
  plan: ObjectIdSchema(),
  patient: ObjectIdSchema(),

  // All other fields from main schema (optional for creation)
  ...Type.Partial(Type.Omit(caresSchema, ['_id', 'person', 'plan', 'patient'])).properties
}, { additionalProperties: false })

export type CaresData = Static<typeof caresDataSchema>
export const caresDataValidator = getValidator(caresDataSchema, dataValidator)
export const caresDataResolver = resolve<CaresData, HookContext>({})

export const caresPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(caresSchema, ['_id'])).properties,

  // MongoDB operators
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $addToSet: Type.Optional(addToSet([
    { path: 'visits', type: ObjectIdSchema() },
    { path: 'providers', type: ObjectIdSchema() },
    { path: 'practitioners', type: ObjectIdSchema() },
    { path: 'children', type: ObjectIdSchema() },
    { path: 'related', type: ObjectIdSchema() },
    { path: 'conditions', type: ExesSchema },
    { path: 'threads', type: ObjectIdSchema() }
  ])),
  $pull: Type.Optional(pull([
    { path: 'visits', type: ObjectIdSchema() },
    { path: 'providers', type: ObjectIdSchema() },
    { path: 'practitioners', type: ObjectIdSchema() },
    { path: 'children', type: ObjectIdSchema() },
    { path: 'related', type: ObjectIdSchema() },
    { path: 'conditions', type: ExesSchema },
    { path: 'threads', type: ObjectIdSchema() }
  ]))
}, { additionalProperties: false })
export type CaresPatch = Static<typeof caresPatchSchema>
export const caresPatchValidator = getValidator(caresPatchSchema, dataValidator)
export const caresPatchResolver = resolve<CaresPatch, HookContext>({})

// Schema for allowed query properties - matching original schema
export const caresQuerySchema = Type.Object({
  ...querySyntax(caresSchema).properties
}, { additionalProperties: false })

export type CaresQuery = Static<typeof caresQuerySchema>
export const caresQueryValidator = getValidator(caresQuerySchema, queryValidator)
export const caresQueryResolver = resolve<CaresQuery, HookContext>({})
