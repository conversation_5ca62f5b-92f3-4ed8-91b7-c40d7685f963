// TypeBox schema for providers service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, PhoneSchema, ImageSchema, VideoSchema, ServiceAddressSchema, GeoJsonFeatureSchema, addToSet, pull } from '../../utils/common/typebox-schemas.js'

// Day time schema
const DaySchema = Type.Object({
  hour: Type.Optional(Type.Number()),
  minute: Type.Optional(Type.Number())
}, { additionalProperties: false })

// Open day schema
const OpenDaySchema = Type.Object({
  open: Type.Optional(DaySchema),
  close: Type.Optional(DaySchema)
}, { additionalProperties: false })

// Opening hours schema (pattern properties for days 0-6)
const OpenHoursSchema = Type.Record(
  Type.String({ pattern: '^[0-6]$' }),
  OpenDaySchema,
  { additionalProperties: false }
)

// License schema for provider licenses
const LicenseSchema = Type.Object({
  state: Type.Optional(Type.String()),
  license: Type.Optional(Type.String()),
  desc: Type.Optional(Type.String()),
  code: Type.Optional(Type.String())
}, { additionalProperties: false })

// City schema for provider cities
const CitySchema = Type.Object({
  city: Type.Optional(Type.String()),
  state: Type.Optional(Type.String())
}, { additionalProperties: false })

// Payment settings schema
const PaymentSettingsSchema = Type.Object({
  card_payments: Type.Optional(Type.String()),
  ach_payments: Type.Optional(Type.Array(ObjectIdSchema())), // account ids
  default_ach: Type.Optional(ObjectIdSchema())
}, { additionalProperties: false })

// Main data model schema
export const providersSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Required fields from original schema
  name: Type.String(), // Required

  // Provider fields from original schema
  org: Type.Optional(ObjectIdSchema()),
  avatar: Type.Optional(ImageSchema),
  legalName: Type.Optional(Type.String()),
  address: Type.Optional(ServiceAddressSchema),
  addresses: Type.Optional(Type.Array(ServiceAddressSchema)),
  email: Type.Optional(Type.String()),
  emails: Type.Optional(Type.Array(Type.String())),
  phone: Type.Optional(PhoneSchema),
  phones: Type.Optional(Type.Array(PhoneSchema)),
  images: Type.Optional(Type.Array(ImageSchema)),
  npi: Type.Optional(Type.String()),
  specialties: Type.Optional(Type.Array(Type.String())),
  patientUpdate: Type.Optional(Type.String()),
  primaryType: Type.Optional(Type.String()), // enum: doctor, dentist, pharmacy, physiotherapist, hospital
  otherTypes: Type.Optional(Type.Array(Type.String())), // same enum
  priority: Type.Optional(Type.Number()),
  tags: Type.Optional(Type.Array(Type.String())),
  licenses: Type.Optional(Type.Array(LicenseSchema)),
  allVideos: Type.Optional(Type.Array(ObjectIdSchema())), // uploadIds

  // CRITICAL Pattern Properties - All 3 were completely lost!
  videos: Type.Optional(Type.Object({
    general: Type.Optional(Type.Record(Type.String(), VideoSchema)), // Pattern: ^.*$
    memberships: Type.Optional(Type.Record(Type.String(), VideoSchema)), // Pattern: ^.*$
    bundles: Type.Optional(Type.Record(Type.String(), VideoSchema)) // Pattern: ^.*$
  })),

  // Additional provider fields from original schema
  bundles: Type.Optional(Type.Array(ObjectIdSchema())),
  bundle_length: Type.Optional(Type.Number()),
  auto_created: Type.Optional(Type.Boolean()),
  npi_status: Type.Optional(Type.String()),
  cities: Type.Optional(Type.Array(CitySchema)),
  typeDescription: Type.Optional(Type.String()),
  autoGenerated: Type.Optional(Type.Boolean()),
  googlePlacesId: Type.Optional(Type.String()),
  customerId: Type.Optional(Type.String()),
  practitioners: Type.Optional(Type.Array(ObjectIdSchema())),
  geo: Type.Optional(GeoJsonFeatureSchema), // multipoint
  locations: Type.Optional(Type.Array(ServiceAddressSchema)),
  cats: Type.Optional(Type.Array(ObjectIdSchema())),
  googleRating: Type.Optional(Type.Number()),
  googleRatingCount: Type.Optional(Type.Number()),
  googleMapsUri: Type.Optional(Type.String()),
  regularHours: Type.Optional(OpenHoursSchema),
  websiteUri: Type.Optional(Type.String()),
  payment_settings: Type.Optional(PaymentSettingsSchema),
      specialties: Type.Optional(Type.Array(Type.String())),
      networks: Type.Optional(Type.Array(ObjectIdSchema())),
      practitioners: Type.Optional(Type.Array(ObjectIdSchema())),
      locations: Type.Optional(Type.Array(ObjectIdSchema())),
      services: Type.Optional(Type.Array(Type.String())),
      features: Type.Optional(Type.Array(GeoJsonFeatureSchema)),
      public: Type.Optional(Type.Boolean()),
      verified: Type.Optional(Type.Boolean()),
      active: Type.Optional(Type.Boolean()),
  // Common fields
  ...commonFields.properties
}, { additionalProperties: false })

export type Providers = Static<typeof providersSchema>
export const providersValidator = getValidator(providersSchema, dataValidator)
export const providersResolver = resolve<Providers, HookContext>({
  // Resolvers from original schema
  priority: async (val, data) => {
    if (!val && data.org) return 1
    return val
  },
  bundle_length: async (val, data) => {
    return data.bundles?.length || val
  },
  typeDescription: async (val) => {
    if (!val) return ''
    return val
  }
})
export const providersExternalResolver = resolve<Providers, HookContext>({})

// Schema for creating new data
export const providersDataSchema = Type.Object({
  ...Type.Omit(providersSchema, ['_id']).properties
}, { additionalProperties: false })

export type ProvidersData = Static<typeof providersDataSchema>
export const providersDataValidator = getValidator(providersDataSchema, dataValidator)
export const providersDataResolver = resolve<ProvidersData, HookContext>({
  // Data resolvers from original schema
  legalName: async (val, data) => {
    if (!val) return data.name
    return val
  },
  googlePlacesId: async (val, data) => {
    if (!val) return `*_${data.name}_${new Date().getTime()}`
    return val
  }
})

// Schema for updating existing data
export const providersPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(providersSchema, ['_id'])).properties,

  // MongoDB operators - matching original pushPull
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $addToSet: Type.Optional(addToSet([
    { path: 'geo', type: Type.Any() },
    { path: 'practitioners', type: ObjectIdSchema() },
    { path: 'locations', type: ServiceAddressSchema },
    { path: 'allVideos', type: ObjectIdSchema() },
    { path: 'bundles', type: ObjectIdSchema() }
  ])),
  $pull: Type.Optional(pull([
    { path: 'geo', type: Type.Any() },
    { path: 'practitioners', type: ObjectIdSchema() },
    { path: 'locations', type: ServiceAddressSchema },
    { path: 'allVideos', type: ObjectIdSchema() },
    { path: 'bundles', type: ObjectIdSchema() }
  ]))
}, { additionalProperties: false })

export type ProvidersPatch = Static<typeof providersPatchSchema>
export const providersPatchValidator = getValidator(providersPatchSchema, dataValidator)
export const providersPatchResolver = resolve<ProvidersPatch, HookContext>({})

// Schema for allowed query properties - matching original schema
export const providersQuerySchema = Type.Object({
  ...querySyntax(providersSchema).properties,
  // Custom query properties from original
  name: Type.Optional(Type.Any()),
  'address.city': Type.Optional(Type.String()),
  'address.region': Type.Optional(Type.String())
}, { additionalProperties: false })

export type ProvidersQuery = Static<typeof providersQuerySchema>
export const providersQueryValidator = getValidator(providersQuerySchema, queryValidator)
export const providersQueryResolver = resolve<ProvidersQuery, HookContext>({})
