// TypeBox schema for markets service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, ServiceAddressSchema } from '../../utils/common/typebox-schemas.js'

// Simplified GeoJSON schema (would need full import in real implementation)
const GeoJsonSchema = Type.Object({
  type: Type.Optional(Type.String()),
  coordinates: Type.Optional(Type.Array(Type.Any()))
}, { additionalProperties: true })

export const marketsSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Geographic market fields from original schema
  name: Type.Optional(Type.String()),
  hosts: Type.Optional(Type.Array(ObjectIdSchema())),
  owners: Type.Optional(Type.Array(ObjectIdSchema())), // Hosts
  managers: Type.Optional(Type.Array(ObjectIdSchema())), // Hosts
  geo: Type.Optional(GeoJsonSchema),

  // CRITICAL Pattern Property - This was completely lost!
  locations: Type.Optional(Type.Record(Type.String({ pattern: '[A-Z]{2}' }), Type.Object({
    cities: Type.Optional(Type.Array(Type.String())),
    zips: Type.Optional(Type.Array(Type.String())),
    counties: Type.Optional(Type.Array(Type.String()))
  }))), // Pattern: [A-Z]{2}

  // Common fields
  ...commonFields.properties
}, { additionalProperties: false })

export type Markets = Static<typeof marketsSchema>
export const marketsValidator = getValidator(marketsSchema, dataValidator)
export const marketsResolver = resolve<Markets, HookContext>({})
export const marketsExternalResolver = resolve<Markets, HookContext>({})

export const marketsDataSchema = Type.Object({
  // No required fields for creation per original schema
  ...Type.Partial(Type.Omit(marketsSchema, ['_id'])).properties
}, { additionalProperties: false })

export type MarketsData = Static<typeof marketsDataSchema>
export const marketsDataValidator = getValidator(marketsDataSchema, dataValidator)
export const marketsDataResolver = resolve<MarketsData, HookContext>({})

export const marketsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(marketsSchema, ['_id'])).properties,

  // MongoDB operators
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any()))
}, { additionalProperties: false })
export type MarketsPatch = Static<typeof marketsPatchSchema>
export const marketsPatchValidator = getValidator(marketsPatchSchema, dataValidator)
export const marketsPatchResolver = resolve<MarketsPatch, HookContext>({})

// Schema for allowed query properties - matching original schema
export const marketsQuerySchema = Type.Object({
  ...querySyntax(marketsSchema).properties
}, { additionalProperties: false })

export type MarketsQuery = Static<typeof marketsQuerySchema>
export const marketsQueryValidator = getValidator(marketsQuerySchema, queryValidator)
export const marketsQueryResolver = resolve<MarketsQuery, HookContext>({})
