// TypeBox schema for ledgers service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

export const ledgersSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Required fields from original schema
  plan: ObjectIdSchema(), // Required
  org: ObjectIdSchema(), // Required
  person: ObjectIdSchema(), // Required
  planYear: Type.Number(), // Required

  // Healthcare account ledger fields from original schema
  type: Type.Optional(Type.Union([
    Type.Literal('fsa'), Type.Literal('hra'),
    Type.Literal('ichra'), Type.Literal('dcap')
  ])),
  account: Type.Optional(ObjectIdSchema()),
  transactions: Type.Optional(Type.Array(ObjectIdSchema())),

  // Common fields
  ...commonFields.properties
}, { additionalProperties: false })

export type Ledgers = Static<typeof ledgersSchema>
export const ledgersValidator = getValidator(ledgersSchema, dataValidator)
export const ledgersResolver = resolve<Ledgers, HookContext>({})
export const ledgersExternalResolver = resolve<Ledgers, HookContext>({})

export const ledgersDataSchema = Type.Object({
  // Required fields for creating new ledgers
  plan: ObjectIdSchema(),
  org: ObjectIdSchema(),
  person: ObjectIdSchema(),
  planYear: Type.Number(),

  // All other fields from main schema (optional for creation)
  ...Type.Partial(Type.Omit(ledgersSchema, ['_id', 'plan', 'org', 'person', 'planYear'])).properties
}, { additionalProperties: false })

export type LedgersData = Static<typeof ledgersDataSchema>
export const ledgersDataValidator = getValidator(ledgersDataSchema, dataValidator)
export const ledgersDataResolver = resolve<LedgersData, HookContext>({})

export const ledgersPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(ledgersSchema, ['_id'])).properties,

  // MongoDB operators
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any()))
}, { additionalProperties: false })
export type LedgersPatch = Static<typeof ledgersPatchSchema>
export const ledgersPatchValidator = getValidator(ledgersPatchSchema, dataValidator)
export const ledgersPatchResolver = resolve<LedgersPatch, HookContext>({})

// Schema for allowed query properties - matching original schema
export const ledgersQuerySchema = Type.Object({
  ...querySyntax(ledgersSchema).properties
}, { additionalProperties: false })

export type LedgersQuery = Static<typeof ledgersQuerySchema>
export const ledgersQueryValidator = getValidator(ledgersQuerySchema, queryValidator)
export const ledgersQueryResolver = resolve<LedgersQuery, HookContext>({})
