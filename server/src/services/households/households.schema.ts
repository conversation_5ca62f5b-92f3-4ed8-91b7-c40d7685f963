// TypeBox schema for households service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, ServiceAddressSchema } from '../../utils/common/typebox-schemas.js'

// Relationships enum from original schema
const relationships = ['self', 'spouse', 'child', 'parent', 'sibling', 'other'] as const

// Enroll extras schema (simplified version of what enrollExtras.properties would contain)
const EnrollExtrasSchema = Type.Object({
  monthsSinceSmoked: Type.Optional(Type.Number()),
  disabled: Type.Optional(Type.Boolean()),
  incarcerated: Type.Optional(Type.Boolean())
}, { additionalProperties: false })

// Main data model schema
export const householdsSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Required fields from original schema
  person: ObjectIdSchema(), // Required

  // Household configuration
  filingAs: Type.Optional(Type.Union([
    Type.Literal('s'), Type.Literal('ms'), Type.Literal('hh'), Type.Literal('mj')
  ])),
  providers: Type.Optional(Type.Array(ObjectIdSchema())),
  practitioners: Type.Optional(Type.Array(ObjectIdSchema())),
  magi: Type.Optional(Type.Number()),

  // CRITICAL Pattern Properties - All 4 were completely lost!
  qual_events: Type.Optional(Type.Record(Type.String(), Type.String())), // Pattern: ^.*$ -> event name key and date of occurrence

  incomes: Type.Optional(Type.Record(Type.String(), Type.Object({
    name: Type.Optional(Type.String()),
    amount: Type.Optional(Type.Number()),
    off: Type.Optional(Type.Boolean()),
    interval: Type.Optional(Type.Union([
      Type.Literal('hour'), Type.Literal('day'), Type.Literal('week'),
      Type.Literal('month'), Type.Literal('quarter'), Type.Literal('year'), Type.Literal('once')
    ])),
    class: Type.Optional(Type.Union([Type.Literal('ee'), Type.Literal('ic')])),
    estHours: Type.Optional(Type.Number())
  }))), // Pattern: ^.*$

  deductions: Type.Optional(Type.Record(Type.String(), Type.Object({
    off: Type.Optional(Type.Boolean()),
    amount: Type.Optional(Type.Number()),
    atl: Type.Optional(Type.Boolean())
  }))), // Pattern: ^.*$

  members: Type.Optional(Type.Record(Type.String(), Type.Object({
    relation: Type.Optional(Type.Union([
      Type.Literal('self'), Type.Literal('spouse'), Type.Literal('child'),
      Type.Literal('parent'), Type.Literal('sibling'), Type.Literal('other')
    ])),
    dependent: Type.Optional(Type.Boolean()),
    annualIncome: Type.Optional(Type.Number()),
    address: Type.Optional(ServiceAddressSchema),
    ...EnrollExtrasSchema.properties
  }))), // Pattern: ^.*$

  // Common fields
  ...commonFields.properties
}, { additionalProperties: false })

export type Households = Static<typeof householdsSchema>
export const householdsValidator = getValidator(householdsSchema, dataValidator)
export const householdsResolver = resolve<Households, HookContext>({})
export const householdsExternalResolver = resolve<Households, HookContext>({})

// Schema for creating new data
export const householdsDataSchema = Type.Object({
  // No required fields for creation per original schema
  ...Type.Partial(Type.Omit(householdsSchema, ['_id'])).properties
}, { additionalProperties: false })

export type HouseholdsData = Static<typeof householdsDataSchema>
export const householdsDataValidator = getValidator(householdsDataSchema, dataValidator)
export const householdsDataResolver = resolve<HouseholdsData, HookContext>({})

// Schema for updating existing data
export const householdsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(householdsSchema, ['_id'])).properties,

  // MongoDB operators
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any()))
}, { additionalProperties: false })

export type HouseholdsPatch = Static<typeof householdsPatchSchema>
export const householdsPatchValidator = getValidator(householdsPatchSchema, dataValidator)
export const householdsPatchResolver = resolve<HouseholdsPatch, HookContext>({})

// Schema for allowed query properties - matching original schema
export const householdsQuerySchema = Type.Object({
  ...querySyntax(householdsSchema).properties
}, { additionalProperties: false })

export type HouseholdsQuery = Static<typeof householdsQuerySchema>
export const householdsQueryValidator = getValidator(householdsQuerySchema, queryValidator)
export const householdsQueryResolver = resolve<HouseholdsQuery, HookContext>({})
