// TypeBox schema for claim-payments service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, ImageSchema, addToSet, pull } from '../../utils/common/typebox-schemas.js'

// Refund schema for claim payment refunds
const RefundSchema = Type.Object({
  amount: Type.Optional(Type.Number()),
  reason: Type.Optional(Type.String()),
  refundedAt: Type.Optional(Type.Any()),
  refundedBy: Type.Optional(ObjectIdSchema()),
  transactionId: Type.Optional(Type.String())
}, { additionalProperties: false })

export const claimPaymentsSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Required fields from original schema - MANY required fields!
  amount: Type.Number(), // Required
  claim: ObjectIdSchema(), // Required
  org: ObjectIdSchema(), // Required
  person: ObjectIdSchema(), // Required
  plan: ObjectIdSchema(), // Required
  patient: ObjectIdSchema(), // Required
  provider: ObjectIdSchema(), // Required

  // Related entities
  visit: Type.Optional(ObjectIdSchema()),
  coverage: Type.Optional(ObjectIdSchema()),
  enrollment: Type.Optional(ObjectIdSchema()),
  providerCareAccount: Type.Optional(ObjectIdSchema()),
  careAccount: Type.Optional(ObjectIdSchema()),
  card: Type.Optional(ObjectIdSchema()),
  budget: Type.Optional(ObjectIdSchema()),

  // Payment configuration
  preventive: Type.Optional(Type.Boolean()),
  allowedMethods: Type.Optional(Type.Array(Type.Union([
    Type.Literal('card'), Type.Literal('ach'), Type.Literal('ca')
  ]))),
  method: Type.Optional(Type.Union([
    Type.Literal('card'), Type.Literal('ach'), Type.Literal('ca'), Type.Literal('ext')
  ])),

  // Payment processing
  checkoutSession: Type.Optional(Type.String()),
  customerId: Type.Optional(Type.String()),
  payment_method: Type.Optional(Type.String()),
  transactionId: Type.Optional(Type.String()),

  // Financial breakdown
  ded: Type.Optional(Type.Number()),
  copay: Type.Optional(Type.Number()),
  coins: Type.Optional(Type.Number()),
  due: Type.Optional(Type.String()),
  refunds: Type.Optional(Type.Array(RefundSchema)),
  type: Type.Optional(Type.String()),
  confirmation: Type.Optional(Type.String()),
  confirmedAt: Type.Optional(Type.Any()),
  fullPayment: Type.Optional(Type.Boolean()),
  status: Type.Optional(Type.Union([
    Type.Literal('request'), Type.Literal('offer'), Type.Literal('pending'),
    Type.Literal('paid'), Type.Literal('cancelled'), Type.Literal('refunded'), Type.Literal('returned')
  ])),

  // CRITICAL Pattern Property - This was completely lost!
  files: Type.Optional(Type.Record(Type.String(), ImageSchema)), // Pattern: ^.*$

  // Common fields
  ...commonFields.properties
}, { additionalProperties: false })

export type ClaimPayments = Static<typeof claimPaymentsSchema>
export const claimPaymentsValidator = getValidator(claimPaymentsSchema, dataValidator)
export const claimPaymentsResolver = resolve<ClaimPayments, HookContext>({})
export const claimPaymentsExternalResolver = resolve<ClaimPayments, HookContext>({})

export const claimPaymentsDataSchema = Type.Object({
  // Required fields for creating new claim payments
  amount: Type.Number(),
  claim: ObjectIdSchema(),
  org: ObjectIdSchema(),
  person: ObjectIdSchema(),
  plan: ObjectIdSchema(),
  patient: ObjectIdSchema(),
  provider: ObjectIdSchema(),

  // All other fields from main schema (optional for creation)
  ...Type.Partial(Type.Omit(claimPaymentsSchema, ['_id', 'amount', 'claim', 'org', 'person', 'plan', 'patient', 'provider'])).properties
}, { additionalProperties: false })

export type ClaimPaymentsData = Static<typeof claimPaymentsDataSchema>
export const claimPaymentsDataValidator = getValidator(claimPaymentsDataSchema, dataValidator)
export const claimPaymentsDataResolver = resolve<ClaimPaymentsData, HookContext>({})

export const claimPaymentsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(claimPaymentsSchema, ['_id'])).properties,

  // MongoDB operators
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $addToSet: Type.Optional(addToSet([
    { path: 'refunds', type: RefundSchema },
    { path: 'allowedMethods', type: Type.Union([Type.Literal('card'), Type.Literal('ach'), Type.Literal('ca')]) }
  ])),
  $pull: Type.Optional(pull([
    { path: 'refunds', type: RefundSchema },
    { path: 'allowedMethods', type: Type.Union([Type.Literal('card'), Type.Literal('ach'), Type.Literal('ca')]) }
  ]))
}, { additionalProperties: false })
export type ClaimPaymentsPatch = Static<typeof claimPaymentsPatchSchema>
export const claimPaymentsPatchValidator = getValidator(claimPaymentsPatchSchema, dataValidator)
export const claimPaymentsPatchResolver = resolve<ClaimPaymentsPatch, HookContext>({})

// Schema for allowed query properties - matching original schema
export const claimPaymentsQuerySchema = Type.Object({
  ...querySyntax(claimPaymentsSchema).properties
}, { additionalProperties: false })

export type ClaimPaymentsQuery = Static<typeof claimPaymentsQuerySchema>
export const claimPaymentsQueryValidator = getValidator(claimPaymentsQuerySchema, queryValidator)
export const claimPaymentsQueryResolver = resolve<ClaimPaymentsQuery, HookContext>({})
