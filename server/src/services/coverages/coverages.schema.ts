// TypeBox schema for coverages service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, ImageSchema, VideoSchema, GeoJsonFeatureSchema, addToSet, pull } from '../../utils/common/typebox-schemas.js'

// Cost limits from original schema
const costLimits = {
  moop: {
    single: 9450, // 2024 ACA limits
    family: 18900
  }
}

// Limit out-of-pocket function from original schema
const limitOop = (val: any) => {
  if (!val) return val
  const { single = 0, family = 0 } = val
  return {
    single: Math.min(single, costLimits.moop.single),
    family: Math.min(family, costLimits.moop.family),
    type: val.type || 'annual'
  }
}

// Deductible schema
const DedSchema = Type.Object({
  name: Type.Optional(Type.String()),
  waivable: Type.Optional(Type.Boolean()),
  detail: Type.Optional(Type.String()),
  cats: Type.Optional(Type.Array(ObjectIdSchema())),
  single: Type.Optional(Type.Number()),
  family: Type.Optional(Type.Number()),
  type: Type.Optional(Type.Union([Type.Literal('event'), Type.Literal('annual')]))
}, { additionalProperties: false })

// Fixed rate schema
export const fixedRate = Type.Object({
  single: Type.Optional(Type.Number()),
  plus_spouse: Type.Optional(Type.Number()),
  plus_child: Type.Optional(Type.Number()),
  plus_child__2: Type.Optional(Type.Number()),
  plus_child__3: Type.Optional(Type.Number()),
  family: Type.Optional(Type.Number())
}, { additionalProperties: false })

// Premium schema with pattern properties
const PremiumSchema = Type.Object({
  flatPremium: Type.Optional(fixedRate),
  rateByAge: Type.Optional(Type.Record(Type.String(), Type.Number())),
  fixedRates: Type.Optional(Type.Record(Type.String(), fixedRate)),
  rateType: Type.Optional(Type.Union([Type.Literal('flatPremium'), Type.Literal('rateByAge'), Type.Literal('fixedRates')])),
  multiDiscount: Type.Optional(Type.Record(Type.String(), Type.Number())), // Pattern: ^(1?[0-9]|20)$
  weights: Type.Optional(Type.Record(Type.String(), Type.Number())), // Pattern: ^(1?[0-9]|20)$
  baseDefault: Type.Optional(Type.Boolean()),
  breakpointAges: Type.Optional(Type.Array(Type.Number())),
  rateBreak: Type.Optional(Type.Union([Type.Literal('graduated'), Type.Literal('breakpoint')])),
  smokerFactor: Type.Optional(Type.Number())
}, { additionalProperties: false })

// Main coverages schema with all pattern properties restored
export const coveragesSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Required fields
  name: Type.String(),
  covered: Type.Union([Type.Literal('individual'), Type.Literal('group')]),
  type: Type.Union([Type.Literal('mm'), Type.Literal('mec'), Type.Literal('hs'), Type.Literal('dc'), Type.Literal('eb'), Type.Literal('hra')]),

  // Basic coverage properties
  carrierName: Type.Optional(Type.String()),
  webpage: Type.Optional(Type.String()),
  openNetwork: Type.Optional(Type.Boolean()),
  plan_type: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  hsaQualified: Type.Optional(Type.Boolean()),
  productDetailRef: Type.Optional(Type.String()),
  fortyPremium: Type.Optional(Type.Number()),
  maxAge: Type.Optional(Type.Number()),
  preventive: Type.Optional(Type.Boolean()),

  // Complex schemas
  deductible: Type.Optional(DedSchema),
  cap: Type.Optional(DedSchema),
  moop: Type.Optional(DedSchema),
  premium: Type.Optional(PremiumSchema),

  // Pattern Properties (dynamic keys) - CRITICAL: These were lost in migration
  multiDiscount: Type.Optional(Type.Record(Type.String(), Type.Number())), // Pattern: ^(1?[0-9]|20)$
  weights: Type.Optional(Type.Record(Type.String(), Type.Number())), // Pattern: ^(1?[0-9]|20)$
  moops: Type.Optional(Type.Record(Type.String(), DedSchema)), // Pattern: ^.*$
  catsBlacklist: Type.Optional(Type.Record(Type.String(), Type.Object({
    id: ObjectIdSchema(),
    memo: Type.String()
  }))), // Pattern: ^.*$
  av: Type.Optional(Type.Record(Type.String(), Type.Object({
    value: Type.Number(),
    by: ObjectIdSchema(),
    at:Type.Optional(Type.Any())
  }))), // Pattern: ^.*$
  networks: Type.Optional(Type.Record(Type.String(), Type.Object({
    coins_discount: Type.Number(),
    coins_discount_type: Type.Union([Type.Literal('percent'), Type.Literal('flat')]),
    ded_discount: Type.Number(),
    ded_discount_type: Type.Union([Type.Literal('percent'), Type.Literal('flat')])
  }))), // Pattern: ^.*$
  vectorIds: Type.Optional(Type.Record(Type.String(), Type.Object({
    uploadIds: Type.Array(ObjectIdSchema()),
    id: Type.String(),
    fileIds: Type.Array(Type.String()),
    updatedAt:Type.Optional(Type.Any())
  }))), // Pattern: ^.*$

  // Additional properties from coverCopySchema
  carrierLogo: Type.Optional(ImageSchema),
  documents: Type.Optional(Type.Array(ImageSchema)),
  postTax: Type.Optional(Type.Boolean()),
  video: Type.Optional(VideoSchema),
  geo: Type.Optional(GeoJsonFeatureSchema),
  issuer: Type.Optional(ObjectIdSchema()),
  org: Type.Optional(ObjectIdSchema()),
  lastSync: Type.Optional(Type.String()),
  provider: Type.Optional(ObjectIdSchema()),
  template: Type.Optional(Type.Boolean()),
  fromTemplate: Type.Optional(ObjectIdSchema()),
  public: Type.Optional(Type.Boolean()),
  sim: Type.Optional(Type.Boolean()),
  group_sim_only: Type.Optional(Type.Boolean()),
  contract: Type.Optional(ObjectIdSchema()),
  listBillDiscount: Type.Optional(Type.Number()),
  dpc: Type.Optional(Type.Boolean()),
  ichra: Type.Optional(Type.Boolean()),
  shop: Type.Optional(Type.Boolean()),
  adj: Type.Optional(Type.Object({
    autoMax: Type.Optional(Type.Number()),
    authProviders: Type.Optional(Type.Array(ObjectIdSchema()))
  })),
  disability: Type.Optional(Type.Object({
    coverOverRequiredAge: Type.Optional(Type.Boolean()),
    incomeLimit: Type.Optional(Type.Number())
  })),
  rates: Type.Optional(Type.Array(ObjectIdSchema())),
  deductibleType: Type.Optional(Type.Union([Type.Literal('annual'), Type.Literal('event')])),
  monthsSinceSmoked: Type.Optional(Type.Number()),

  // Common fields
  ...commonFields.properties
}, { additionalProperties: false })

export type Coverages = Static<typeof coveragesSchema>
export const coveragesValidator = getValidator(coveragesSchema, dataValidator)
export const coveragesResolver = resolve<Coverages, HookContext>({
  // Resolvers from original schema
  moop: async (val) => {
    return limitOop(val)
  },
  deductible: async (val) => {
    return limitOop(val)
  }
})
export const coveragesExternalResolver = resolve<Coverages, HookContext>({})

// Schema for creating new data
export const coveragesDataSchema = Type.Object({
  // Required fields for creating new coverages
  type: Type.Union([Type.Literal('medical'), Type.Literal('dental'), Type.Literal('vision'), Type.Literal('life'), Type.Literal('disability')]),
  covered: Type.Union([Type.Literal('individual'), Type.Literal('group')]),
  name: Type.String(),

  // All other fields from main schema (optional for creation)
  ...Type.Partial(Type.Omit(coveragesSchema, ['_id', 'type', 'covered', 'name'])).properties
}, { additionalProperties: false })

export type CoveragesData = Static<typeof coveragesDataSchema>
export const coveragesDataValidator = getValidator(coveragesDataSchema, dataValidator)
export const coveragesDataResolver = resolve<CoveragesData, HookContext>({})

// Schema for updating existing data - matches original pattern
export const coveragesPatchSchema = Type.Object({
  // All schema properties (for direct field updates)
  ...Type.Omit(coveragesSchema, ['_id']).properties,

  // Partial schema properties (for optional updates)
  ...Type.Partial(Type.Omit(coveragesSchema, ['_id'])).properties,

  // MongoDB operators
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $addToSet: Type.Optional(addToSet([
    { path: 'rates', type: ObjectIdSchema() }
  ])),
  $pull: Type.Optional(pull([
    { path: 'rates', type: ObjectIdSchema() }
  ]))
}, { additionalProperties: false })
export type CoveragesPatch = Static<typeof coveragesPatchSchema>
export const coveragesPatchValidator = getValidator(coveragesPatchSchema, dataValidator)
export const coveragesPatchResolver = resolve<CoveragesPatch, HookContext>({})

// Schema for allowed query properties with custom query fields
export const coveragesQuerySchema = Type.Object({
  ...querySyntax(Type.Object({
    ...coveragesSchema.properties,
    // Custom query properties from original schema
    name:Type.Optional(Type.Any()),
    geo:Type.Optional(Type.Any()),
    'geo.geometry':Type.Optional(Type.Any()),
    fortyPremium:Type.Optional(Type.Any())
  })).properties,

  // Complex query operators for specific fields
  'carrierLogo.uploadId': Type.Optional(Type.Union([
    ObjectIdSchema(),
    Type.Object({
      $in: Type.Optional(Type.Array(ObjectIdSchema())),
      $nin: Type.Optional(Type.Array(ObjectIdSchema()))
    })
  ]))
}, { additionalProperties: true })

export type CoveragesQuery = Static<typeof coveragesQuerySchema>
export const coveragesQueryValidator = getValidator(coveragesQuerySchema, queryValidator)
export const coveragesQueryResolver = resolve<CoveragesQuery, HookContext>({})

// Export for backward compatibility
export const coverCopySchema = coveragesSchema
