// TypeBox schema for cobras service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

export const cobrasSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Required fields from original schema
  enrollment: ObjectIdSchema(), // Required
  participant: ObjectIdSchema(), // Required
  household: ObjectIdSchema(), // Required
  deadline: Type.String(), // Required

  // COBRA configuration
  spec: Type.Optional(ObjectIdSchema()),
  event_type: Type.Optional(Type.Union([
    Type.Literal('job'), Type.Literal('death'), Type.Literal('divorce'),
    Type.Literal('medicare'), Type.Literal('dependent')
  ])),
  end_date: Type.Optional(Type.String()),
  optOut: Type.Optional(Type.Boolean()),

  // CRITICAL Pattern Property - This was completely lost!
  coverages: Type.Optional(Type.Record(Type.String(), Type.Object({
    recurs: Type.Number(),
    participants: Type.Array(ObjectIdSchema())
  }))), // Pattern: ^.*$

  // Common fields
  ...commonFields.properties
}, { additionalProperties: false })

export type Cobras = Static<typeof cobrasSchema>
export const cobrasValidator = getValidator(cobrasSchema, dataValidator)
export const cobrasResolver = resolve<Cobras, HookContext>({})
export const cobrasExternalResolver = resolve<Cobras, HookContext>({})

export const cobrasDataSchema = Type.Object({
  // Required fields for creating new COBRAs (excluding _id per original)
  enrollment: ObjectIdSchema(),
  participant: ObjectIdSchema(),
  household: ObjectIdSchema(),
  deadline: Type.String(),

  // All other fields from main schema (optional for creation)
  ...Type.Partial(Type.Omit(cobrasSchema, ['_id', 'enrollment', 'participant', 'household', 'deadline'])).properties
}, { additionalProperties: false })

export type CobrasData = Static<typeof cobrasDataSchema>
export const cobrasDataValidator = getValidator(cobrasDataSchema, dataValidator)
export const cobrasDataResolver = resolve<CobrasData, HookContext>({})

export const cobrasPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(cobrasSchema, ['_id'])).properties,

  // MongoDB operators
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any()))
}, { additionalProperties: false })
export type CobrasPatch = Static<typeof cobrasPatchSchema>
export const cobrasPatchValidator = getValidator(cobrasPatchSchema, dataValidator)
export const cobrasPatchResolver = resolve<CobrasPatch, HookContext>({})

// Schema for allowed query properties - matching original schema
export const cobrasQuerySchema = Type.Object({
  ...querySyntax(cobrasSchema).properties
}, { additionalProperties: false })

export type CobrasQuery = Static<typeof cobrasQuerySchema>
export const cobrasQueryValidator = getValidator(cobrasQuerySchema, queryValidator)
export const cobrasQueryResolver = resolve<CobrasQuery, HookContext>({})
