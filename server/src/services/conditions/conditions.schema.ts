// TypeBox schema for conditions service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

export const conditionsSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Condition reference fields from original schema
  standard: Type.Optional(Type.String()),
  code: Type.Optional(Type.String()),
  link: Type.Optional(Type.String()),
  chapter: Type.Optional(Type.String()),
  name: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  procedures: Type.Optional(Type.Array(ObjectIdSchema())),
  procedureComments: Type.Optional(Type.String()),

  // Common fields
  ...commonFields.properties
}, { additionalProperties: false })

export type Conditions = Static<typeof conditionsSchema>
export const conditionsValidator = getValidator(conditionsSchema, dataValidator)
export const conditionsResolver = resolve<Conditions, HookContext>({})
export const conditionsExternalResolver = resolve<Conditions, HookContext>({})

export const conditionsDataSchema = Type.Object({
  ...Type.Partial(Type.Omit(conditionsSchema, ['_id'])).properties
}, { additionalProperties: false })

export type ConditionsData = Static<typeof conditionsDataSchema>
export const conditionsDataValidator = getValidator(conditionsDataSchema, dataValidator)
export const conditionsDataResolver = resolve<ConditionsData, HookContext>({})

export const conditionsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(conditionsSchema, ['_id'])).properties,

  // MongoDB operators
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any()))
}, { additionalProperties: false })
export type ConditionsPatch = Static<typeof conditionsPatchSchema>
export const conditionsPatchValidator = getValidator(conditionsPatchSchema, dataValidator)
export const conditionsPatchResolver = resolve<ConditionsPatch, HookContext>({})

// Schema for allowed query properties - matching original schema
export const conditionsQuerySchema = Type.Object({
  ...querySyntax(conditionsSchema).properties
}, { additionalProperties: false })

export type ConditionsQuery = Static<typeof conditionsQuerySchema>
export const conditionsQueryValidator = getValidator(conditionsQuerySchema, queryValidator)
export const conditionsQueryResolver = resolve<ConditionsQuery, HookContext>({})
