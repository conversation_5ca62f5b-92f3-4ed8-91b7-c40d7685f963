// TypeBox schema for meds service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

// Related info schema with pattern property for medication relationships
const RelatedInfoSchema = Type.Record(Type.String(), Type.Object({
  rxcui: Type.Optional(Type.String()),
  name: Type.Optional(Type.String()),
  synonym: Type.Optional(Type.String()),
  language: Type.Optional(Type.String())
}, { additionalProperties: true })) // Pattern: ^.*$

export const medsSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Required fields from original schema
  rxcui: Type.String(), // Required

  // Medication reference fields from original schema
  standard: Type.Optional(Type.String()),
  rxcuis: Type.Optional(Type.Array(Type.String())),
  s_f: Type.Optional(Type.Array(Type.String())), // strengths and forms
  variants: Type.Optional(Type.Array(Type.String())),
  name: Type.Optional(Type.String()),
  medical_name: Type.Optional(Type.String()),
  consumer_name: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  activeIngredient: Type.Optional(Type.String()),
  synonyms: Type.Optional(Type.String()),
  sbdOf: Type.Optional(ObjectIdSchema()),
  ndcs: Type.Optional(Type.Array(Type.String())),

  // CRITICAL Complex Object - This was completely lost!
  info: Type.Optional(Type.Object({
    'IN': Type.Optional(RelatedInfoSchema),
    'PIN': Type.Optional(RelatedInfoSchema),
    'MIN': Type.Optional(RelatedInfoSchema),
    'SCD': Type.Optional(RelatedInfoSchema),
    'SCDF': Type.Optional(RelatedInfoSchema),
    'SCDG': Type.Optional(RelatedInfoSchema),
    'SCDC': Type.Optional(RelatedInfoSchema),
    'GPCK': Type.Optional(RelatedInfoSchema),
    'BN': Type.Optional(RelatedInfoSchema),
    'BPCK': Type.Optional(RelatedInfoSchema),
    'DF': Type.Optional(RelatedInfoSchema),
    'DFG': Type.Optional(RelatedInfoSchema),
    'SBD': Type.Optional(RelatedInfoSchema),
    'SBDG': Type.Optional(RelatedInfoSchema),
    'SBDC': Type.Optional(RelatedInfoSchema),
    'SBDF': Type.Optional(RelatedInfoSchema),
    'SBDFP': Type.Optional(RelatedInfoSchema)
  })),

  // Common fields
  ...commonFields.properties
}, { additionalProperties: false })

export type Meds = Static<typeof medsSchema>
export const medsValidator = getValidator(medsSchema, dataValidator)
export const medsResolver = resolve<Meds, HookContext>({})
export const medsExternalResolver = resolve<Meds, HookContext>({})

export const medsDataSchema = Type.Object({
  // No required fields for creation per original schema
  ...Type.Partial(Type.Omit(medsSchema, ['_id'])).properties
}, { additionalProperties: false })

export type MedsData = Static<typeof medsDataSchema>
export const medsDataValidator = getValidator(medsDataSchema, dataValidator)
export const medsDataResolver = resolve<MedsData, HookContext>({})

export const medsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(medsSchema, ['_id'])).properties,

  // MongoDB operators
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any()))
}, { additionalProperties: false })
export type MedsPatch = Static<typeof medsPatchSchema>
export const medsPatchValidator = getValidator(medsPatchSchema, dataValidator)
export const medsPatchResolver = resolve<MedsPatch, HookContext>({})

// Schema for allowed query properties - matching original schema
export const medsQuerySchema = Type.Object({
  ...querySyntax(medsSchema).properties
}, { additionalProperties: false })

export type MedsQuery = Static<typeof medsQuerySchema>
export const medsQueryValidator = getValidator(medsQuerySchema, queryValidator)
export const medsQueryResolver = resolve<MedsQuery, HookContext>({})
