// TypeBox schema for flow-charts service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

// Flow chart node schema (flexible schema for dynamic flow chart nodes)
const FlowChartNodeSchema = Type.Object({
  parent: Type.Optional(Type.String()),
  label: Type.Optional(Type.String()),
  children: Type.Optional(Type.Object({}, { additionalProperties: true })),
  q: Type.Optional(Type.String()),
  text: Type.Optional(Type.String())
}, { additionalProperties: true })

export const flowChartsSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Flow chart fields from original schema
  name: Type.Optional(Type.String()),
  nodes: Type.Optional(Type.Array(FlowChartNodeSchema)),

  // Common fields
  ...commonFields.properties
}, { additionalProperties: true })

export type FlowCharts = Static<typeof flowChartsSchema>
export const flowChartsValidator = getValidator(flowChartsSchema, dataValidator)
export const flowChartsResolver = resolve<FlowCharts, HookContext>({})
export const flowChartsExternalResolver = resolve<FlowCharts, HookContext>({})

export const flowChartsDataSchema = Type.Object({
  // No required fields for creation per original schema
  ...Type.Partial(Type.Omit(flowChartsSchema, ['_id'])).properties
}, { additionalProperties: true })

export type FlowChartsData = Static<typeof flowChartsDataSchema>
export const flowChartsDataValidator = getValidator(flowChartsDataSchema, dataValidator)
export const flowChartsDataResolver = resolve<FlowChartsData, HookContext>({})

export const flowChartsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(flowChartsSchema, ['_id'])).properties,

  // MongoDB operators
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any()))
}, { additionalProperties: true })
export type FlowChartsPatch = Static<typeof flowChartsPatchSchema>
export const flowChartsPatchValidator = getValidator(flowChartsPatchSchema, dataValidator)
export const flowChartsPatchResolver = resolve<FlowChartsPatch, HookContext>({})

// Schema for allowed query properties - matching original schema
export const flowChartsQuerySchema = Type.Object({
  ...querySyntax(flowChartsSchema).properties
}, { additionalProperties: true })

export type FlowChartsQuery = Static<typeof flowChartsQuerySchema>
export const flowChartsQueryValidator = getValidator(flowChartsQuerySchema, queryValidator)
export const flowChartsQueryResolver = resolve<FlowChartsQuery, HookContext>({})
