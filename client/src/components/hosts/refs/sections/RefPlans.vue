<template>
  <div class="_fw">

  </div>
</template>

<script setup>

  import {computed} from 'vue';
  import {HFind} from 'src/utils/hFind';
  import {usePlans} from 'stores/plans';
  import {idGet} from 'src/utils/id-get';
  import {useHosts} from 'stores/hosts';
  import {useContracts} from 'stores/contracts';
  import {useAtcStore} from 'src/stores/atc-store';

  const planStore = usePlans();
  const hostStore = useHosts();
  const contractStore = useContracts();

  const props = defineProps({
    modelValue: { required: true }
  })

  const reF = computed(() => props.modelValue || {})

  const { item:host } = idGet({
    store: hostStore,
    value: computed(() => reF.value?.host)
  ,
    useAtcStore
  })

  const teamIds = computed(() => Object.values(host.value.plans || {}).map(a => a.team).filter(a => !!a && (reF.value.teams || {})[a]))
  // const { h$:t$ } = HFind({
  //   store: teamStore,
  //   params: computed(() => {
  //     return {
  //       query: {
  //         $limit: teamIds.value.length,
  //         _id: { $in: teamIds.value }
  //       }
  //     }
  //   })
  // })

  const { h$:c$ } = HFind({
    store: contractStore,
    params: computed(() => {
      const idList = Object.values(host.value.plans || {}).map(a => a.payContract)
      return {
        query: {
          _id: { $in: idList},
          $limit: idList.length,
          [`pay.refSplit.${reF.value._id}.ref`]: reF.value._id
        }
      }
    })
  })

  const { h$:p$ } = HFind({
    store: planStore,
    params: computed(() => {
      const idList = Object.values(host.value.plans || {}).filter(a => teamIds.value.includes(a.team) || c$.data.map(a => a.subject).includes(a))
      return {
        query: {
          _id: { $in: idList }
        }
      }
    })
  })

</script>

<style lang="scss" scoped>

</style>
