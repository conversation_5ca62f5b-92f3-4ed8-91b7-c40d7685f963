// TypeBox schema for leads service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, PhoneSchema, ServiceAddressSchema } from '../../utils/common/typebox-schemas.js'

export const leadsSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Minimal schema matching original - just common fields with flexible data
  ...commonFields.properties
}, { additionalProperties: true })

export type Leads = Static<typeof leadsSchema>
export const leadsValidator = getValidator(leadsSchema, dataValidator)
export const leadsResolver = resolve<Leads, HookContext>({})
export const leadsExternalResolver = resolve<Leads, HookContext>({})

export const leadsDataSchema = Type.Object({
  // No required fields for creation per original schema
  ...Type.Partial(Type.Omit(leadsSchema, ['_id'])).properties
}, { additionalProperties: true })

export type LeadsData = Static<typeof leadsDataSchema>
export const leadsDataValidator = getValidator(leadsDataSchema, dataValidator)
export const leadsDataResolver = resolve<LeadsData, HookContext>({})

export const leadsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(leadsSchema, ['_id'])).properties,

  // MongoDB operators
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any()))
}, { additionalProperties: true })
export type LeadsPatch = Static<typeof leadsPatchSchema>
export const leadsPatchValidator = getValidator(leadsPatchSchema, dataValidator)
export const leadsPatchResolver = resolve<LeadsPatch, HookContext>({})

// Schema for allowed query properties - matching original schema
export const leadsQuerySchema = Type.Object({
  ...querySyntax(leadsSchema).properties
}, { additionalProperties: true })

export type LeadsQuery = Static<typeof leadsQuerySchema>
export const leadsQueryValidator = getValidator(leadsQuerySchema, queryValidator)
export const leadsQueryResolver = resolve<LeadsQuery, HookContext>({})
