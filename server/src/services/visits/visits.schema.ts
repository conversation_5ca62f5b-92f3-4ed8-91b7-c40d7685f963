// TypeBox schema for visits service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, ImageSchema, addToSet, pull } from '../../utils/common/typebox-schemas.js'

// Coins categories enum from original schema
const coinsCategories = [
  'office', 'specialist', 'urgent', 'er', 'inpatient', 'outpatient',
  'lab', 'imaging', 'pharmacy', 'mental', 'maternity'
] as const

// Exes schema for conditions
const ExesSchema = Type.Object({
  code: Type.Optional(Type.String()),
  description: Type.Optional(Type.String())
}, { additionalProperties: false })

// Paids schema properties from original
const PaidsSchema = Type.Object({
  paidAmount: Type.Optional(Type.Number()),
  paidAt: Type.Optional(Type.Any()),
  paidBy: Type.Optional(ObjectIdSchema()),
  paidMethod: Type.Optional(Type.String())
}, { additionalProperties: false })

// Entered by schema
const EnteredBySchema = Type.Object({
  login: Type.Optional(ObjectIdSchema()),
  fingerprint: Type.Optional(ObjectIdSchema()),
  at: Type.Optional(Type.Any())
}, { additionalProperties: false })

// Main data model schema
export const visitsSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Required fields from original schema
  provider: ObjectIdSchema(), // Required
  patient: ObjectIdSchema(), // Required
  care: ObjectIdSchema(), // Required
  plan: ObjectIdSchema(), // Required
  date: Type.Any(), // Required

  // Healthcare visit fields from original schema
  person: Type.Optional(ObjectIdSchema()),
  preventive: Type.Optional(Type.Boolean()),
  status: Type.Optional(Type.Union([
    Type.Literal('appointment'), Type.Literal('record'), Type.Literal('cancelled')
  ])),
  endDate: Type.Optional(Type.Any()),
  category: Type.Optional(Type.Union([
    Type.Literal('office'), Type.Literal('specialist'), Type.Literal('urgent'),
    Type.Literal('er'), Type.Literal('inpatient'), Type.Literal('outpatient'),
    Type.Literal('lab'), Type.Literal('imaging'), Type.Literal('pharmacy'),
    Type.Literal('mental'), Type.Literal('maternity')
  ])),
  er: Type.Optional(Type.Boolean()),
  conditions: Type.Optional(Type.Array(ExesSchema)),
  claims: Type.Optional(Type.Array(ObjectIdSchema())),
  claimReqs: Type.Optional(Type.Array(ObjectIdSchema())),

  // Financial fields
  total: Type.Optional(Type.Number()),
  subtotal: Type.Optional(Type.Number()),
  ...PaidsSchema.properties,
  balance: Type.Optional(Type.Number()),
  balanceSyncedAt: Type.Optional(Type.Any()),

  // Visit management
  enteredBy: Type.Optional(EnteredBySchema),
  practitioners: Type.Optional(Type.Array(ObjectIdSchema())),
  threads: Type.Optional(Type.Array(ObjectIdSchema())),

  files: Type.Optional(Type.Record(Type.String(), ImageSchema)), // Pattern: ^.*$
  ...commonFields.properties
}, { additionalProperties: false })

export type Visits = Static<typeof visitsSchema>
export const visitsValidator = getValidator(visitsSchema, dataValidator)
export const visitsResolver = resolve<Visits, HookContext>({
  // Resolvers from original schema
  date: async (val) => {
    if (!val) return new Date()
    return val
  },
  status: async (val, data) => {
    if (!val) {
      if (data.date) {
        if (new Date(data.date as any).getTime() < new Date().getTime()) {
          return 'appointment'
        } else {
          return 'record'
        }
      }
    }
    return val
  }
})
export const visitsExternalResolver = resolve<Visits, HookContext>({})

// Schema for creating new data
export const visitsDataSchema = Type.Object({
  // No required fields for creation per original schema
  ...Type.Partial(Type.Omit(visitsSchema, ['_id'])).properties
}, { additionalProperties: false })

export type VisitsData = Static<typeof visitsDataSchema>
export const visitsDataValidator = getValidator(visitsDataSchema, dataValidator)
export const visitsDataResolver = resolve<VisitsData, HookContext>({})

// Schema for updating existing data
export const visitsPatchSchema = Type.Object({
  patient: Type.Optional(ObjectIdSchema()),
  person: Type.Optional(ObjectIdSchema()),
  provider: Type.Optional(ObjectIdSchema()),
  practitioner: Type.Optional(ObjectIdSchema()),
  date: Type.Optional(Type.Any()),
  scheduledDate: Type.Optional(Type.Any()),
  type: Type.Optional(Type.String()),
  status: Type.Optional(Type.String()),
  reason: Type.Optional(Type.String()),
  diagnosis: Type.Optional(Type.String()),
  notes: Type.Optional(Type.String()),
  enteredBy: Type.Optional(EnteredBySchema),
  duration: Type.Optional(Type.Number()),
  location: Type.Optional(Type.String()),
  room: Type.Optional(Type.String()),
  vitals: Type.Optional(Type.Record(Type.String(), Type.Any())),
  procedures: Type.Optional(Type.Array(ObjectIdSchema())),
  medications: Type.Optional(Type.Array(ObjectIdSchema())),
  followUp: Type.Optional(Type.String()),
  referrals: Type.Optional(Type.Array(ObjectIdSchema())),
  attachments: Type.Optional(Type.Array(Type.Any())),
  billing: Type.Optional(Type.Record(Type.String(), Type.Any())),
  insurance: Type.Optional(Type.Record(Type.String(), Type.Any())),
  copay: Type.Optional(Type.Number()),
  coinsurance: Type.Optional(Type.Number()),
  deductible: Type.Optional(Type.Number()),
  total: Type.Optional(Type.Number()),
  paid: Type.Optional(Type.Number()),
  balance: Type.Optional(Type.Number()),
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $push: Type.Optional(Type.Object({
    procedures: Type.Optional(ObjectIdSchema()),
    medications: Type.Optional(ObjectIdSchema()),
    referrals: Type.Optional(ObjectIdSchema()),
    attachments: Type.Optional(Type.Any())
  ,
  // Missing MongoDB operators
  $addToSet: Type.Optional(addToSet([])),
}, { additionalProperties: false })),
  $pull: Type.Optional(Type.Object({
    procedures: Type.Optional(ObjectIdSchema()),
    medications: Type.Optional(ObjectIdSchema()),
    referrals: Type.Optional(ObjectIdSchema()),
    attachments: Type.Optional(Type.Any())
  }, { additionalProperties: false }))
}, { additionalProperties: false })

export type VisitsPatch = Static<typeof visitsPatchSchema>
export const visitsPatchValidator = getValidator(visitsPatchSchema, dataValidator)
export const visitsPatchResolver = resolve<VisitsPatch, HookContext>({})

// Schema for allowed query properties
// Allow querying on any field from the main schema
const visitsQueryProperties = visitsSchema

export const visitsQuerySchema = querySyntax(visitsQueryProperties)

export type VisitsQuery = Static<typeof visitsQuerySchema>
export const visitsQueryValidator = getValidator(visitsQuerySchema, queryValidator)
export const visitsQueryResolver = resolve<VisitsQuery, HookContext>({})
