// TypeBox schema for uploads service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, addToSet, pull } from '../../utils/common/typebox-schemas.js'

// Usage schema from original schema
const UsageSchema = Type.Object({
  subject: Type.Optional(ObjectIdSchema()),
  subjectModel: Type.Optional(Type.String()),
  subjectPath: Type.Optional(Type.String()),
  subjectArray: Type.Optional(Type.Boolean())
}, { additionalProperties: false })

export const uploadsSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Required fields from original schema
  url: Type.String(), // Required
  fileId: Type.String(), // Required
  storage: Type.String(), // Required

  // Upload tracking fields from original schema
  name: Type.Optional(Type.String()),
  cid: Type.Optional(Type.String()),
  originalName: Type.Optional(Type.String()),
  originalname: Type.Optional(Type.String()),
  expires: Type.Optional(Type.Number()), // seconds for links to last until expiring
  usageVerified: Type.Optional(Type.String()),
  usage: Type.Optional(Type.Array(UsageSchema)),
  info: Type.Optional(Type.Object({
    name: Type.Optional(Type.String()),
    size: Type.Optional(Type.Number()),
    type: Type.Optional(Type.String()),
    lastModifiedDate: Type.Optional(Type.Any())
  })),
  session: Type.Optional(Type.String()),
  temp: Type.Optional(Type.Boolean()),
  video: Type.Optional(Type.Boolean()),
  status: Type.Optional(Type.String()),
  bucket: Type.Optional(Type.Union([Type.String(), Type.Number()])),
  uploadChannel: Type.Optional(Type.String()),
  tags: Type.Optional(Type.Array(Type.String())),

  // Common fields
  ...commonFields.properties
}, { additionalProperties: true })

export type Uploads = Static<typeof uploadsSchema>
export const uploadsValidator = getValidator(uploadsSchema, dataValidator)
export const uploadsResolver = resolve<Uploads, HookContext>({})
export const uploadsExternalResolver = resolve<Uploads, HookContext>({})

export const uploadsDataSchema = Type.Object({
  // No required fields for creation per original schema
  ...Type.Partial(Type.Omit(uploadsSchema, ['_id'])).properties
}, { additionalProperties: true })

export type UploadsData = Static<typeof uploadsDataSchema>
export const uploadsDataValidator = getValidator(uploadsDataSchema, dataValidator)
export const uploadsDataResolver = resolve<UploadsData, HookContext>({})

export const uploadsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(uploadsSchema, ['_id'])).properties,

  // MongoDB operators - matching original $addToSet/$pull for usage and tags
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $addToSet: Type.Optional(addToSet([
    { path: 'usage', type: UsageSchema },
    { path: 'tags', type: Type.String() }
  ])),
  $pull: Type.Optional(pull([
    { path: 'usage', type: UsageSchema },
    { path: 'tags', type: Type.String() }
  ]))
}, { additionalProperties: true })
export type UploadsPatch = Static<typeof uploadsPatchSchema>
export const uploadsPatchValidator = getValidator(uploadsPatchSchema, dataValidator)
export const uploadsPatchResolver = resolve<UploadsPatch, HookContext>({})

// Schema for allowed query properties - matching original schema
export const uploadsQuerySchema = Type.Object({
  ...querySyntax(uploadsSchema).properties
}, { additionalProperties: true })

export type UploadsQuery = Static<typeof uploadsQuerySchema>
export const uploadsQueryValidator = getValidator(uploadsQuerySchema, queryValidator)
export const uploadsQueryResolver = resolve<UploadsQuery, HookContext>({})
