// TypeBox schema for bills service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, ServiceAddressSchema, PhoneSchema, ImageSchema, TaxSchema } from '../../utils/common/typebox-schemas.js'

// Line item schema for bills
const LineItemSchema = Type.Object({
  description: Type.Optional(Type.String()),
  quantity: Type.Optional(Type.Number()),
  unitPrice: Type.Optional(Type.Number()),
  amount: Type.Optional(Type.Number()),
  category: Type.Optional(Type.String())
}, { additionalProperties: false })

// RRule schema for recurrence
const RRuleSchema = Type.Object({
  freq: Type.Optional(Type.String()),
  interval: Type.Optional(Type.Number()),
  count: Type.Optional(Type.Number()),
  until: Type.Optional(Type.Any())
}, { additionalProperties: false })

export const billsSchema = Type.Object({
  _id: ObjectIdSchema(),

  // To/From contact information (all optional per original)
  to: Type.Optional(ObjectIdSchema()),
  toName: Type.Optional(Type.String()),
  toModel: Type.Optional(Type.Union([Type.Literal('orgs'), Type.Literal('ppls')])),
  toEmail: Type.Optional(Type.String()),
  toAddress: Type.Optional(ServiceAddressSchema),
  toPhone: Type.Optional(PhoneSchema),
  from: Type.Optional(ObjectIdSchema()),
  fromName: Type.Optional(Type.String()),
  formModel: Type.Optional(Type.Union([Type.Literal('orgs'), Type.Literal('ppls')])),
  fromEmail: Type.Optional(Type.String()),
  fromAddress: Type.Optional(ServiceAddressSchema),
  fromPhone: Type.Optional(PhoneSchema),

  // Bill details
  logo: Type.Optional(ImageSchema),
  billDate: Type.Optional(Type.Any()),
  dueDate: Type.Optional(Type.Any()),
  currency: Type.Optional(Type.Union([Type.Literal('usd')])),
  lastTaxTotal: Type.Optional(Type.Number()),
  lineItems: Type.Optional(Type.Array(LineItemSchema)),
  threads: Type.Optional(Type.Array(ObjectIdSchema())),

  // Settings with tax configuration
  settings: Type.Optional(Type.Object({
    tax: Type.Optional(TaxSchema)
  })),

  payments: Type.Optional(Type.Array(ObjectIdSchema())),
  recurrence: Type.Optional(RRuleSchema),
  paymentLink: Type.Optional(Type.String()),
  status: Type.Optional(Type.Union([
    Type.Literal('open'),
    Type.Literal('paid'),
    Type.Literal('closed')
  ])),

  // CRITICAL Pattern Property - files with imageSchema
  files: Type.Optional(Type.Record(Type.String(), ImageSchema)), // Pattern: ^.*$

  // Common fields
  ...commonFields.properties
}, { additionalProperties: false })

export type Bills = Static<typeof billsSchema>
export const billsValidator = getValidator(billsSchema, dataValidator)
export const billsResolver = resolve<Bills, HookContext>({})
export const billsExternalResolver = resolve<Bills, HookContext>({})

export const billsDataSchema = Type.Object({
  // No required fields for creation per original schema
  ...Type.Partial(Type.Omit(billsSchema, ['_id'])).properties
}, { additionalProperties: false })

export type BillsData = Static<typeof billsDataSchema>
export const billsDataValidator = getValidator(billsDataSchema, dataValidator)
export const billsDataResolver = resolve<BillsData, HookContext>({})

export const billsPatchSchema = Type.Object({
  // All schema properties (for direct field updates)
  ...Type.Omit(billsSchema, ['_id']).properties,

  // Partial schema properties (for optional updates)
  ...Type.Partial(Type.Omit(billsSchema, ['_id'])).properties,

  // Common MongoDB operators
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any()))
}, { additionalProperties: false })

export type BillsPatch = Static<typeof billsPatchSchema>
export const billsPatchValidator = getValidator(billsPatchSchema, dataValidator)
export const billsPatchResolver = resolve<BillsPatch, HookContext>({})

// Schema for allowed query properties - matching original schema
export const billsQuerySchema = Type.Object({
  ...querySyntax(billsSchema).properties
}, { additionalProperties: false })

export type BillsQuery = Static<typeof billsQuerySchema>
export const billsQueryValidator = getValidator(billsQuerySchema, queryValidator)
export const billsQueryResolver = resolve<BillsQuery, HookContext>({})
