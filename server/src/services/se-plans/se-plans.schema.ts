// TypeBox schema for se-plans service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, addToSet } from '../../utils/common/typebox-schemas.js'

// Norm policy schema (simplified - would need full import)
const NormPolicySchema = Type.Object({
  // Simplified norm policy fields - full schema would be imported
  deductible: Type.Optional(Type.Number()),
  copay: Type.Optional(Type.Number()),
  coinsurance: Type.Optional(Type.Number())
}, { additionalProperties: true })

export const sePlansSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Required fields from original schema
  name: Type.String(), // Required
  plan_id: Type.String(), // Required
  state_code: Type.String(), // Required

  // SE plan fields from original schema
  org: Type.Optional(ObjectIdSchema()),
  public: Type.Optional(Type.Boolean()),
  template: Type.Optional(Type.Boolean()),
  fromTemplate: Type.Optional(ObjectIdSchema()),
  sim: Type.Optional(Type.Boolean()),
  rateIncrease: Type.Optional(Type.Object({
    amount: Type.Optional(Type.Number()),
    date: Type.Optional(Type.Any())
  })),
  all_fips: Type.Optional(Type.Array(Type.String())),
  first_3_zips: Type.Optional(Type.Array(Type.String())), // First 3 digits of all zips
  all_zips: Type.Optional(Type.Array(Type.String())), // All zips in rating areas
  fortyPremium: Type.Optional(Type.Number()),
  design: Type.Optional(Type.String()),

  // Norm policy fields spread
  ...NormPolicySchema.properties,

  // CRITICAL Pattern Properties - Both were completely lost!
  rating_areas: Type.Optional(Type.Record(Type.String({ pattern: '^\\d+$' }), Type.Object({
    name: Type.Optional(Type.String()),
    zips: Type.Optional(Type.Array(Type.String())),
    fips: Type.Optional(Type.Array(Type.String())),
    cities: Type.Optional(Type.Array(Type.String())),
    county: Type.Optional(Type.String()),
    rates: Type.Optional(Type.Record(Type.String({ pattern: '^([0-9]|[1-9][0-9]|1[01][0-9]|120)$' }), Type.Number())) // Nested pattern: ages 0-120
  }))), // Pattern: ^\d+$

  csr: Type.Optional(Type.Record(Type.String({ pattern: '^(02|03|04)$' }), NormPolicySchema)), // Pattern: ^(02|03|04)$

  // Common fields
  ...commonFields.properties
}, { additionalProperties: false })

export type SePlans = Static<typeof sePlansSchema>
export const sePlansValidator = getValidator(sePlansSchema, dataValidator)
export const sePlansResolver = resolve<SePlans, HookContext>({})
export const sePlansExternalResolver = resolve<SePlans, HookContext>({})

export const sePlansDataSchema = Type.Object({
  // Required fields for creating new SE plans
  name: Type.String(),
  plan_id: Type.String(),
  state_code: Type.String(),

  // All other fields from main schema (optional for creation)
  ...Type.Partial(Type.Omit(sePlansSchema, ['_id', 'name', 'plan_id', 'state_code'])).properties
}, { additionalProperties: false })

export type SePlansData = Static<typeof sePlansDataSchema>
export const sePlansDataValidator = getValidator(sePlansDataSchema, dataValidator)
export const sePlansDataResolver = resolve<SePlansData, HookContext>({})

export const sePlansPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(sePlansSchema, ['_id'])).properties,

  // MongoDB operators
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $addToSet: Type.Optional(addToSet([
    { path: 'all_fips', type: Type.String() },
    { path: 'first_3_zips', type: Type.String() },
    { path: 'all_zips', type: Type.String() }
  ])),
  $pull: Type.Optional(Type.Record(Type.String(), Type.Any()))
}, { additionalProperties: false })
export type SePlansPatch = Static<typeof sePlansPatchSchema>
export const sePlansPatchValidator = getValidator(sePlansPatchSchema, dataValidator)
export const sePlansPatchResolver = resolve<SePlansPatch, HookContext>({})

// Schema for allowed query properties - matching original schema with custom properties
export const sePlansQuerySchema = Type.Object({
  ...querySyntax(sePlansSchema).properties,

  // Custom query properties from original schema
  name: Type.Optional(Type.Any()),
  carrierName: Type.Optional(Type.Any()),
  plan_id: Type.Optional(Type.Any()),
  fortyPremium: Type.Optional(Type.Any()),
  rating_areas: Type.Optional(Type.Any())
}, { additionalProperties: false })

export type SePlansQuery = Static<typeof sePlansQuerySchema>
export const sePlansQueryValidator = getValidator(sePlansQuerySchema, queryValidator)
export const sePlansQueryResolver = resolve<SePlansQuery, HookContext>({})
