// TypeBox schema for comps service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, ImageSchema } from '../../utils/common/typebox-schemas.js'


// Contact schema for compensation contact info
const ContactSchema = Type.Object({
  name: Type.Optional(Type.String()),
  phone: Type.Optional(Type.String()),
  email: Type.Optional(Type.String())
}, { additionalProperties: false })

export const compsInterval = Type.UnionType.Union([
  Type.Literal('hour'), Type.Literal('day'), Type.Literal('week'),
  Type.Literal('month'), Type.Literal('quarter'), Type.Literal('year'), Type.Literal('once')
])
export const compsCamsSchema = {
  org: ObjectIdSchema(),
  name: Type.String(),
  contract: Type.Optional(ObjectIdSchema()),
  estHours: Type.Optional(Type.Number()),
  terms: Type.Optional(Type.String()),
  class: Type.Optional(Type.Union([Type.Literal('ee'), Type.Literal('ic')])),
  extras: Type.Optional(Type.Record(Type.String(), Type.Object({
    due: Type.Optional(Type.String()),
    awarded: Type.Optional(Type.String()),
    off: Type.Optional(Type.Boolean()),
    banks: Type.Optional(Type.Boolean()),
    type: Type.Optional(Type.Union([Type.Literal('percent'), Type.Literal('flat'), Type.Literal('units')])),
    unit: Type.Optional(Type.Union([
      Type.Literal('hour'), Type.Literal('day'), Type.Literal('week'),
      Type.Literal('month'), Type.Literal('quarter'), Type.Literal('year'), Type.Literal('once')
    ])),
    amount: Type.Optional(Type.Number()),
    interval: Type.Optional(Type.Union([
      Type.Literal('hour'), Type.Literal('day'), Type.Literal('week'),
      Type.Literal('month'), Type.Literal('quarter'), Type.Literal('year'), Type.Literal('once')
    ])),
    terms: Type.Optional(Type.String()),
    limit: Type.Optional(Type.Number())
  }))), // Pattern: ^.*$ (from compsCamsSchema)
}

export const compsSchema = Type.Object({
  _id: ObjectIdSchema(),
  amount: Type.Number(),
  key: Type.String(), // Required
  interval: compsInterval,

  // Compensation configuration
  video: Type.Optional(ImageSchema),
  references: Type.Optional(Type.Array(ObjectIdSchema())),
  ad: Type.Optional(Type.String()),
  geo: Type.Optional(Type.Object({
    type: Type.Optional(Type.String()),
    coordinates: Type.Optional(Type.Array(Type.Number()))
  })),
  contact: Type.Optional(ContactSchema),
  access: Type.Optional(Type.Union([Type.Literal('public'), Type.Literal('private')])),

  // Fields from compsCamsSchema
  contract: Type.Optional(ObjectIdSchema()),
  estHours: Type.Optional(Type.Number()),
  terms: Type.Optional(Type.String()),
  class: Type.Optional(Type.Union([Type.Literal('ee'), Type.Literal('ic')])),

  // CRITICAL Pattern Properties - Both were completely lost!
  stages: Type.Optional(Type.Record(Type.String(), Type.Object({
    label: Type.Optional(Type.String()),
    color: Type.Optional(Type.String())
  }))), // Pattern: ^.*$

  ...compsCamsSchema,
  // Common fields
  ...commonFields.properties
}, { additionalProperties: false })

export type Comps = Static<typeof compsSchema>
export const compsValidator = getValidator(compsSchema, dataValidator)
export const compsResolver = resolve<Comps, HookContext>({
  // Default resolver from original schema
  estHours: async (val) => {
    // setDefEstUnits logic - set default estimated hours
    if (!val) return 40 // Default 40 hours
    return val
  }
})
export const compsExternalResolver = resolve<Comps, HookContext>({})

export const compsDataSchema = Type.Object({
  // Required fields for creating new comps
  amount: Type.Number(),
  interval: Type.Union([
    Type.Literal('hour'), Type.Literal('day'), Type.Literal('week'),
    Type.Literal('month'), Type.Literal('quarter'), Type.Literal('year'), Type.Literal('once')
  ]),
  org: ObjectIdSchema(),
  name: Type.String(),
  key: Type.String(),

  // All other fields from main schema (optional for creation)
  ...Type.Partial(Type.Omit(compsSchema, ['_id', 'amount', 'interval', 'org', 'name', 'key'])).properties
}, { additionalProperties: false })

export type CompsData = Static<typeof compsDataSchema>
export const compsDataValidator = getValidator(compsDataSchema, dataValidator)
export const compsDataResolver = resolve<CompsData, HookContext>({})

export const compsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(compsSchema, ['_id'])).properties,

  // MongoDB operators
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any()))
}, { additionalProperties: false })
export type CompsPatch = Static<typeof compsPatchSchema>
export const compsPatchValidator = getValidator(compsPatchSchema, dataValidator)
export const compsPatchResolver = resolve<CompsPatch, HookContext>({})

// Schema for allowed query properties - matching original schema
export const compsQuerySchema = Type.Object({
  ...querySyntax(compsSchema).properties
}, { additionalProperties: false })

export type CompsQuery = Static<typeof compsQuerySchema>
export const compsQueryValidator = getValidator(compsQuerySchema, queryValidator)
export const compsQueryResolver = resolve<CompsQuery, HookContext>({})
