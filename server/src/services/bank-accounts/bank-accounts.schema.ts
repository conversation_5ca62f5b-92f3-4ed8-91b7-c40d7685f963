// TypeBox schema for bank-accounts service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

export const bankAccountsSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Required fields from original schema
  owner: ObjectIdSchema(), // Required
  type: Type.Union([Type.Literal('business'), Type.Literal('individual')]), // Required enum
  accountNumber: Type.String(), // Required
  routingNumber: Type.String(), // Required

  // Optional fields
  nickname: Type.Optional(Type.String()),
  hsa: Type.Optional(Type.Boolean()),
  description: Type.Optional(Type.String()),
  terms_accepted: Type.Optional(Type.String()),
  business_rep: Type.Optional(ObjectIdSchema()),
  last4: Type.Optional(Type.String()),
  accountType: Type.Optional(Type.Union([Type.Literal('checking'), Type.Literal('savings')])),
  bankName: Type.Optional(Type.String()),
  canAch: Type.Optional(Type.Boolean()),
  canWire: Type.Optional(Type.Boolean()),

  // Complex nested objects
  verification: Type.Optional(Type.Object({
    exceptionDetails: Type.Optional(Type.Object({
      achReturnCode: Type.Optional(Type.String()),
      description: Type.Optional(Type.String()),
      rtpRejectionCode: Type.Optional(Type.String())
    })),
    code: Type.Optional(Type.String()),
    verified: Type.Optional(Type.Boolean()),
    status: Type.Optional(Type.Union([
      Type.Literal('new'), Type.Literal('sent-credit'), Type.Literal('failed'),
      Type.Literal('not-sent'), Type.Literal('verified'), Type.Literal('max-attempts-exceeded'),
      Type.Literal('expired'), Type.Literal('successful')
    ])),
    verificationMethod: Type.Optional(Type.Union([
      Type.Literal('instant'), Type.Literal('ach'), Type.Literal('micro')
    ]))
  })),

  mandate: Type.Optional(Type.Object({
    acceptedAt: Type.Optional(Type.Any()),
    ip: Type.Optional(Type.String()),
    ua: Type.Optional(Type.String()),
    copy: Type.Optional(Type.String())
  })),

  // Additional fields
  setupIntent: Type.Optional(Type.String()),
  latestPaymentIntent: Type.Optional(Type.String()),
  fca: Type.Optional(Type.String()),
  moov_link_id: Type.Optional(Type.String()),
  status: Type.Optional(Type.String()),

  // Common fields
  ...commonFields.properties
}, { additionalProperties: false })

export type BankAccounts = Static<typeof bankAccountsSchema>
export const bankAccountsValidator = getValidator(bankAccountsSchema, dataValidator)
export const bankAccountsResolver = resolve<BankAccounts, HookContext>({})
export const bankAccountsExternalResolver = resolve<BankAccounts, HookContext>({})

export const bankAccountsDataSchema = Type.Object({
  // Required fields for creating new bank accounts
  owner: ObjectIdSchema(),
  accountNumber: Type.String(),
  routingNumber: Type.String(),
  bankName: Type.String(),
  type: Type.Union([Type.Literal('business'), Type.Literal('individual')]),

  // All other fields from main schema (optional for creation)
  ...Type.Partial(Type.Omit(bankAccountsSchema, ['_id', 'owner', 'accountNumber', 'routingNumber', 'type'])).properties
}, { additionalProperties: false })

export type BankAccountsData = Static<typeof bankAccountsDataSchema>
export const bankAccountsDataValidator = getValidator(bankAccountsDataSchema, dataValidator)
export const bankAccountsDataResolver = resolve<BankAccountsData, HookContext>({})

export const bankAccountsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(bankAccountsSchema, ['_id'])).properties,

  // MongoDB operators
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any()))
}, { additionalProperties: false })
export type BankAccountsPatch = Static<typeof bankAccountsPatchSchema>
export const bankAccountsPatchValidator = getValidator(bankAccountsPatchSchema, dataValidator)
export const bankAccountsPatchResolver = resolve<BankAccountsPatch, HookContext>({})

// Schema for allowed query properties - matching original schema
export const bankAccountsQuerySchema = Type.Object({
  ...querySyntax(bankAccountsSchema).properties,
  nickname: Type.Optional(Type.Any()),
  last4: Type.Optional(Type.Any()),
  bankName: Type.Optional(Type.Any())
}, { additionalProperties: false })
export type BankAccountsQuery = Static<typeof bankAccountsQuerySchema>
export const bankAccountsQueryValidator = getValidator(bankAccountsQuerySchema, queryValidator)
export const bankAccountsQueryResolver = resolve<BankAccountsQuery, HookContext>({})
