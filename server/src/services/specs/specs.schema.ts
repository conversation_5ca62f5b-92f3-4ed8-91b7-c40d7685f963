// TypeBox schema for specs service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

export const specsSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Required fields from original schema
  org: ObjectIdSchema(), // Required
  plan: ObjectIdSchema(), // Required
  planYear: Type.String(), // Required

  // Special enrollment specification fields from original schema
  enrollment: Type.Optional(ObjectIdSchema()),
  approvedBy: Type.Optional(ObjectIdSchema()),
  approvedAt: Type.Optional(Type.Any()),
  event: Type.Optional(Type.Union([
    Type.Literal('household'), Type.Literal('employment'),
    Type.Literal('plan'), Type.Literal('hsa')
  ])),
  description: Type.Optional(Type.String()),
  status: Type.Optional(Type.Union([
    Type.Literal('approved'), Type.Literal('rejected'),
    Type.Literal('pending'), Type.Literal('archived')
  ])),
  message: Type.Optional(Type.String()),
  thread: Type.Optional(ObjectIdSchema()),
  changedAt: Type.Optional(Type.Any()),
  specialEnrollment: Type.Optional(Type.Boolean()),

  // CRITICAL Pattern Property - This was completely lost!
  changes: Type.Optional(Type.Record(Type.String(), Type.Object({
    newVal: Type.Optional(Type.Any()), // recursiveAnyOf
    oldVal: Type.Optional(Type.Any())  // recursiveAnyOf
  }))), // Pattern: ^.*$

  // Common fields
  ...commonFields.properties
}, { additionalProperties: false })

export type Specs = Static<typeof specsSchema>
export const specsValidator = getValidator(specsSchema, dataValidator)
export const specsResolver = resolve<Specs, HookContext>({
  // Status resolver from original schema
  status: async (val) => {
    return val || 'pending'
  }
})
export const specsExternalResolver = resolve<Specs, HookContext>({})

export const specsDataSchema = Type.Object({
  // No required fields for creation per original schema
  ...Type.Partial(Type.Omit(specsSchema, ['_id'])).properties
}, { additionalProperties: false })

export type SpecsData = Static<typeof specsDataSchema>
export const specsDataValidator = getValidator(specsDataSchema, dataValidator)
export const specsDataResolver = resolve<SpecsData, HookContext>({
  // Status resolver from original schema
  status: async (val) => {
    return val || 'pending'
  }
})

export const specsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(specsSchema, ['_id'])).properties,

  // MongoDB operators
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any()))
}, { additionalProperties: false })
export type SpecsPatch = Static<typeof specsPatchSchema>
export const specsPatchValidator = getValidator(specsPatchSchema, dataValidator)
export const specsPatchResolver = resolve<SpecsPatch, HookContext>({})

// Schema for allowed query properties - matching original schema with commonQueries
export const specsQuerySchema = Type.Object({
  ...querySyntax(specsSchema).properties
  // Note: commonQueries.properties would be added here in full implementation
}, { additionalProperties: false })

export type SpecsQuery = Static<typeof specsQuerySchema>
export const specsQueryValidator = getValidator(specsQuerySchema, queryValidator)
export const specsQueryResolver = resolve<SpecsQuery, HookContext>({})
