// TypeBox schema for sales-taxes service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

// Tax item schema for taxes array
const TaxItemSchema = Type.Object({
  name: Type.Optional(Type.String()),
  rate: Type.Optional(Type.Number()),
  type: Type.Optional(Type.Union([Type.Literal('percent'), Type.Literal('flat')]))
}, { additionalProperties: false })

export const salesTaxesSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Required fields from original schema
  postal_code: Type.String(), // Required
  taxes: Type.Array(TaxItemSchema), // Required
  state: Type.String(), // Required (two letter)

  // Sales tax fields from original schema
  country: Type.Optional(Type.String()), // Two letter
  city: Type.Optional(Type.String()),
  total_tax: Type.Optional(Type.Number()),
  expires: Type.Optional(Type.String()),

  // Common fields
  ...commonFields.properties
}, { additionalProperties: false })

export type SalesTaxes = Static<typeof salesTaxesSchema>
export const salesTaxesValidator = getValidator(salesTaxesSchema, dataValidator)
export const salesTaxesResolver = resolve<SalesTaxes, HookContext>({})
export const salesTaxesExternalResolver = resolve<SalesTaxes, HookContext>({})

export const salesTaxesDataSchema = Type.Object({
  // Required fields for creating new sales taxes
  postal_code: Type.String(),
  taxes: Type.Array(TaxItemSchema),
  state: Type.String(),

  // All other fields from main schema (optional for creation)
  ...Type.Partial(Type.Omit(salesTaxesSchema, ['_id', 'postal_code', 'taxes', 'state'])).properties
}, { additionalProperties: false })

export type SalesTaxesData = Static<typeof salesTaxesDataSchema>
export const salesTaxesDataValidator = getValidator(salesTaxesDataSchema, dataValidator)
export const salesTaxesDataResolver = resolve<SalesTaxesData, HookContext>({})

export const salesTaxesPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(salesTaxesSchema, ['_id'])).properties,

  // MongoDB operators
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any()))
}, { additionalProperties: false })
export type SalesTaxesPatch = Static<typeof salesTaxesPatchSchema>
export const salesTaxesPatchValidator = getValidator(salesTaxesPatchSchema, dataValidator)
export const salesTaxesPatchResolver = resolve<SalesTaxesPatch, HookContext>({})

// Allow querying on any field from the main schema
const salesTaxesQueryProperties = salesTaxesSchema
export const salesTaxesQuerySchema = querySyntax(salesTaxesQueryProperties)
export type SalesTaxesQuery = Static<typeof salesTaxesQuerySchema>
export const salesTaxesQueryValidator = getValidator(salesTaxesQuerySchema, queryValidator)
export const salesTaxesQueryResolver = resolve<SalesTaxesQuery, HookContext>({})
