#!/usr/bin/env node

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)
const serverDir = path.dirname(__dirname)

// Services with patternProperties that need to be restored
const patternProperties = {
  "coverages": {
    "multiDiscount": {
      "pattern": "^(1?[0-9]|20)$",
      "type": "Type.Number()"
    },
    "weights": {
      "pattern": "^(1?[0-9]|20)$", 
      "type": "Type.Number()"
    },
    "moops": {
      "pattern": "^.*$",
      "type": "DedSchema"
    },
    "catsBlacklist": {
      "pattern": "^.*$",
      "type": "Type.Object({ id: ObjectIdSchema(), memo: Type.String() })"
    },
    "av": {
      "pattern": "^.*$",
      "type": "Type.Object({ value: Type.Number(), by: ObjectIdSchema(), at: Type.Any() })"
    },
    "networks": {
      "pattern": "^.*$",
      "type": "Type.Object({ coins_discount: Type.Number(), coins_discount_type: Type.Union([Type.Literal('percent'), Type.Literal('flat')]), ded_discount: Type.Number(), ded_discount_type: Type.Union([Type.Literal('percent'), Type.Literal('flat')]) })"
    },
    "vectorIds": {
      "pattern": "^.*$",
      "type": "Type.Object({ uploadIds: Type.Array(ObjectIdSchema()), id: Type.String(), fileIds: Type.Array(Type.String()), updatedAt: Type.Any() })"
    }
  },
  "ppls": {
    "moovAccounts": {
      "pattern": "^.*$",
      "type": "Type.Record(Type.String(), Type.Any())"
    },
    "invites": {
      "pattern": "^.*$",
      "type": "Type.Object({ by: ObjectIdSchema(), at: Type.Any(), reminded: Type.Array(Type.Any()), caps: Type.Record(Type.String(), Type.Object({ id: ObjectIdSchema(), path: Type.String() })) })"
    }
  },
  "caps": {
    "caps": {
      "pattern": "^.*$",
      "type": "Type.Object({ description: Type.String(), ucan: Type.String(), logins: Type.Array(ObjectIdSchema()) })"
    }
  },
  "wallets": {
    "methods": {
      "pattern": "^.*$",
      "type": "Type.Object({ id: Type.String(), name: Type.String() })"
    }
  },
  "bills": {
    "files": {
      "pattern": "^.*$",
      "type": "ImageSchema"
    }
  },
  "orgs": {
    "wallets": {
      "pattern": "^.*$",
      "type": "Type.Object({ name: Type.String(), id: ObjectIdSchema(), default: Type.Boolean() })"
    },
    "asg": {
      "pattern": "^.*$",
      "type": "Type.Object({ type: Type.Union([Type.Literal('A'), Type.Literal('B')]), orgs: Type.Record(Type.String(), Type.Any()) })"
    },
    "caps": {
      "pattern": "^.*$",
      "type": "Type.Record(Type.String(), Type.Any())"
    },
    "files": {
      "pattern": "^.*$",
      "type": "ImageSchema"
    }
  },
  "se-plans": {
    "rating_areas": {
      "pattern": "^\\d+$",
      "type": "Type.Object({ name: Type.String(), zips: Type.Array(Type.String()), fips: Type.Array(Type.String()), cities: Type.Array(Type.String()), county: Type.String(), rates: Type.Record(Type.String(), Type.Number()) })"
    },
    "files": {
      "pattern": "^.*$",
      "type": "ImageSchema"
    }
  },
  "shops": {
    "dist": {
      "pattern": "^.*$",
      "type": "Type.Record(Type.String(), Type.Number())"
    },
    "choices": {
      "pattern": "^.*$",
      "type": "Type.Record(Type.String(), Type.Any())"
    }
  },
  "gps": {
    "coverages": {
      "pattern": "^.*$",
      "type": "Type.Object({ id: Type.String(), compare_id: Type.String(), similar: Type.Record(Type.String(), Type.String()), mostSimilar: Type.String() })"
    },
    "employerContributionReports": {
      "pattern": "^.*$",
      "type": "Type.Object({ person: ObjectIdSchema(), gps: ObjectIdSchema() })"
    }
  },
  "claims": {
    "taxes": {
      "pattern": "^.*$",
      "type": "Type.Record(Type.String(), Type.Any())"
    },
    "procedures": {
      "pattern": "^.*$",
      "type": "Type.Record(Type.String(), Type.Any())"
    },
    "files": {
      "pattern": "^.*$",
      "type": "ImageSchema"
    }
  },
  "plans": {
    "vectorStoreIds": {
      "pattern": "^.*$",
      "type": "Type.Record(Type.String(), Type.Any())"
    },
    "team": {
      "pattern": "^.*$",
      "type": "Type.Record(Type.String(), Type.Any())"
    },
    "files": {
      "pattern": "^.*$",
      "type": "ImageSchema"
    }
  },
  "hosts": {
    "shopStatuses": {
      "pattern": "^.*$",
      "type": "Type.Record(Type.String(), Type.Any())"
    },
    "plans": {
      "pattern": "^.*$",
      "type": "Type.Record(Type.String(), Type.Any())"
    },
    "intro": {
      "pattern": "^.*$",
      "type": "Type.Record(Type.String(), Type.Any())"
    },
    "counties": {
      "pattern": "^.*$",
      "type": "Type.Record(Type.String(), Type.Any())"
    }
  }
}

function toCamelCase(str) {
  return str.replace(/-([a-z])/g, (g) => g[1].toUpperCase())
}

function restorePatternProperties(serviceName) {
  const patterns = patternProperties[serviceName]
  if (!patterns) {
    return false
  }
  
  const schemaPath = path.join(serverDir, 'src', 'services', serviceName, `${serviceName}.schema.ts`)
  if (!fs.existsSync(schemaPath)) {
    console.log(`❌ ${serviceName}: Schema file not found`)
    return false
  }
  
  try {
    let content = fs.readFileSync(schemaPath, 'utf8')
    const camelName = toCamelCase(serviceName)
    
    console.log(`🔧 ${serviceName}: Restoring ${Object.keys(patterns).length} pattern properties`)
    
    // Find the main schema and replace pattern properties
    const schemaPattern = new RegExp(`(export const ${camelName}Schema = Type\\.Object\\({[\\s\\S]*?)(}\\s*,\\s*{[\\s\\S]*?}\\))`)
    const schemaMatch = content.match(schemaPattern)
    
    if (schemaMatch) {
      const beforeClosing = schemaMatch[1]
      const afterClosing = schemaMatch[2]
      
      // Build pattern properties
      const patternPropsStr = Object.entries(patterns).map(([key, config]) => {
        if (config.pattern === '^.*$') {
          // Any string key
          return `  ${key}: Type.Optional(Type.Record(Type.String(), ${config.type})),`
        } else {
          // Specific pattern - use Record with comment
          return `  ${key}: Type.Optional(Type.Record(Type.String(), ${config.type})), // Pattern: ${config.pattern}`
        }
      }).join('\n')
      
      // Replace any existing pattern properties and add new ones
      let newSchemaContent = beforeClosing
      
      // Remove any existing pattern properties that are now being replaced
      Object.keys(patterns).forEach(key => {
        const regex = new RegExp(`\\s*${key}:\\s*[^,]+,?`, 'g')
        newSchemaContent = newSchemaContent.replace(regex, '')
      })
      
      // Add pattern properties
      newSchemaContent += ',\n  // Pattern Properties (dynamic keys)\n' + patternPropsStr + '\n' + afterClosing
      
      content = content.replace(schemaMatch[0], newSchemaContent)
      
      fs.writeFileSync(schemaPath, content)
      console.log(`✅ ${serviceName}: Restored ${Object.keys(patterns).length} pattern properties`)
      return true
      
    } else {
      console.log(`❌ ${serviceName}: Could not find schema pattern`)
      return false
    }
    
  } catch (error) {
    console.log(`❌ ${serviceName}: Error restoring pattern properties - ${error.message}`)
    return false
  }
}

// Process all services with pattern properties
console.log(`🚨 CRITICAL: Restoring pattern properties for ${Object.keys(patternProperties).length} services...\n`)

let fixedCount = 0
Object.keys(patternProperties).forEach(serviceName => {
  if (restorePatternProperties(serviceName)) {
    fixedCount++
  }
})

console.log(`\n📊 Results:`)
console.log(`✅ Restored pattern properties: ${fixedCount}/${Object.keys(patternProperties).length} services`)
console.log(`🎯 Critical dynamic object structures have been restored!`)

if (fixedCount > 0) {
  console.log(`\n🔍 Run compilation test to verify the fixes`)
}
