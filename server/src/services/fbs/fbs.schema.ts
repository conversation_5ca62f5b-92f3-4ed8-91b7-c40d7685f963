// TypeBox schema for fbs service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, ImageSchema, addToSet, pull } from '../../utils/common/typebox-schemas.js'

// Simplified field schema (would need full import in real implementation)
const FieldSchema = Type.Object({
  // Simplified field schema
  name: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  required: Type.Optional(Type.Boolean())
}, { additionalProperties: true })

export const fbsSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Form builder fields from original schema
  active: Type.Optional(Type.Boolean()),
  owner: Type.Optional(ObjectIdSchema()),
  org: Type.Optional(ObjectIdSchema()),
  live: Type.Optional(Type.Boolean()),
  parent: Type.Optional(ObjectIdSchema()),
  children: Type.Optional(Type.Array(ObjectIdSchema())),
  name: Type.Optional(Type.String()),
  primaryColor: Type.Optional(Type.String()),
  secondaryColor: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  avatar: Type.Optional(ImageSchema),

  // Welcome section
  welcomeTitle: Type.Optional(Type.String()),
  welcomeMessage: Type.Optional(Type.String()),
  welcomeImage: Type.Optional(ImageSchema),
  welcomeVideos: Type.Optional(Type.Array(ImageSchema)),
  welcomeFiles: Type.Optional(Type.Array(ImageSchema)),

  // Finish section
  finishTitle: Type.Optional(Type.String()),
  finishMessage: Type.Optional(Type.String()),
  finishImage: Type.Optional(ImageSchema),
  finishVideos: Type.Optional(Type.Array(ImageSchema)),
  finishFiles: Type.Optional(Type.Array(ImageSchema)),

  // Form configuration
  dark: Type.Optional(Type.Boolean()),
  class: Type.Optional(Type.String()),
  products: Type.Optional(Type.Object({
    ids: Type.Optional(Type.Array(Type.Any()))
  })),
  style: Type.Optional(Type.Object({
    background: Type.Optional(Type.String()),
    color: Type.Optional(Type.String()),
    padding: Type.Optional(Type.String())
  })),
  fields: Type.Optional(Type.Array(FieldSchema)),
  responses: Type.Optional(Type.Array(ObjectIdSchema())),
  canEdit: Type.Optional(Type.Array(ObjectIdSchema())),

  // Common fields
  ...commonFields.properties
}, { additionalProperties: false })

export type Fbs = Static<typeof fbsSchema>
export const fbsValidator = getValidator(fbsSchema, dataValidator)
export const fbsResolver = resolve<Fbs, HookContext>({})
export const fbsExternalResolver = resolve<Fbs, HookContext>({})

export const fbsDataSchema = Type.Object({
  // No required fields for creation per original schema
  ...Type.Partial(Type.Omit(fbsSchema, ['_id'])).properties
}, { additionalProperties: false })

export type FbsData = Static<typeof fbsDataSchema>
export const fbsDataValidator = getValidator(fbsDataSchema, dataValidator)
export const fbsDataResolver = resolve<FbsData, HookContext>({})

export const fbsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(fbsSchema, ['_id'])).properties,

  // MongoDB operators - matching original pushPull for multiple arrays
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $addToSet: Type.Optional(addToSet([
    { path: 'children', type: ObjectIdSchema() },
    { path: 'responses', type: ObjectIdSchema() },
    { path: 'canEdit', type: ObjectIdSchema() },
    { path: 'welcomeVideos', type: ImageSchema },
    { path: 'welcomeFiles', type: ImageSchema },
    { path: 'finishVideos', type: ImageSchema },
    { path: 'finishFiles', type: ImageSchema }
  ])),
  $pull: Type.Optional(pull([
    { path: 'children', type: ObjectIdSchema() },
    { path: 'responses', type: ObjectIdSchema() },
    { path: 'canEdit', type: ObjectIdSchema() },
    { path: 'welcomeVideos', type: ImageSchema },
    { path: 'welcomeFiles', type: ImageSchema },
    { path: 'finishVideos', type: ImageSchema },
    { path: 'finishFiles', type: ImageSchema }
  ]))
}, { additionalProperties: false })
export type FbsPatch = Static<typeof fbsPatchSchema>
export const fbsPatchValidator = getValidator(fbsPatchSchema, dataValidator)
export const fbsPatchResolver = resolve<FbsPatch, HookContext>({})

// Schema for allowed query properties - matching original schema
export const fbsQuerySchema = Type.Object({
  ...querySyntax(fbsSchema).properties
}, { additionalProperties: false })

export type FbsQuery = Static<typeof fbsQuerySchema>
export const fbsQueryValidator = getValidator(fbsQuerySchema, queryValidator)
export const fbsQueryResolver = resolve<FbsQuery, HookContext>({})
