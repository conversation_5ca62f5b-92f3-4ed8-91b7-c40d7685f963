// TypeBox schema for threads service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

// Owner schema for thread owners
const OwnerSchema = Type.Object({
  did: Type.Optional(Type.String()),
  id: Type.Optional(ObjectIdSchema()),
  name: Type.Optional(Type.String()),
  email: Type.Optional(Type.String()),
  phone: Type.Optional(Type.String())
}, { additionalProperties: false })

// Tag schema for thread tags
const TagSchema = Type.Object({
  name: Type.Optional(Type.String()),
  id: Type.Optional(ObjectIdSchema()),
  service: Type.Optional(Type.String())
}, { additionalProperties: false })

export const threadsSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Required fields from original schema
  parent: Type.Object({
    id: ObjectIdSchema(),
    service: Type.String()
  }), // Required

  // Discussion thread fields from original schema
  upVotes: Type.Optional(Type.Array(ObjectIdSchema())),
  downVotes: Type.Optional(Type.Array(ObjectIdSchema())),
  voteCount: Type.Optional(Type.Number()),
  body: Type.Optional(Type.String()),
  name: Type.Optional(Type.String()),
  did: Type.Optional(Type.String()),
  archives: Type.Optional(Type.Object({
    body: Type.Optional(Type.Array(Type.String()))
  })),
  owners: Type.Optional(Type.Array(OwnerSchema)),
  tags: Type.Optional(Type.Array(TagSchema)),
  threads: Type.Optional(Type.Array(ObjectIdSchema())),

  // Common fields
  ...commonFields.properties
}, { additionalProperties: true })

export type Threads = Static<typeof threadsSchema>
export const threadsValidator = getValidator(threadsSchema, dataValidator)
export const threadsResolver = resolve<Threads, HookContext>({})
export const threadsExternalResolver = resolve<Threads, HookContext>({})

export const threadsDataSchema = Type.Object({
  // No required fields for creation per original schema
  ...Type.Partial(Type.Omit(threadsSchema, ['_id'])).properties
}, { additionalProperties: true })

export type ThreadsData = Static<typeof threadsDataSchema>
export const threadsDataValidator = getValidator(threadsDataSchema, dataValidator)
export const threadsDataResolver = resolve<ThreadsData, HookContext>({})

export const threadsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(threadsSchema, ['_id'])).properties,

  // MongoDB operators
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any()))
}, { additionalProperties: true })
export type ThreadsPatch = Static<typeof threadsPatchSchema>
export const threadsPatchValidator = getValidator(threadsPatchSchema, dataValidator)
export const threadsPatchResolver = resolve<ThreadsPatch, HookContext>({})

// Schema for allowed query properties - matching original schema
export const threadsQuerySchema = Type.Object({
  ...querySyntax(threadsSchema).properties
}, { additionalProperties: true })

export type ThreadsQuery = Static<typeof threadsQuerySchema>
export const threadsQueryValidator = getValidator(threadsQuerySchema, queryValidator)
export const threadsQueryResolver = resolve<ThreadsQuery, HookContext>({})
