// TypeBox schema for groups service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, addToSet, pull } from '../../utils/common/typebox-schemas.js'

// Main data model schema
export const groupsSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Required fields from original schema
  org: ObjectIdSchema(), // Required
  name: Type.String(), // Required
  key: Type.String(), // Required

  // Group configuration from original schema
  description: Type.Optional(Type.String()),
  applyUcan: Type.Optional(Type.String()), // ucan token
  healthPlans: Type.Optional(Type.Array(ObjectIdSchema())),
  planClass: Type.Optional(Type.Boolean()),
  memberCount: Type.Optional(Type.Number()),
  memberCountAt: Type.Optional(Type.Any()),

  // Common fields
  ...commonFields.properties
}, { additionalProperties: false })

export type Groups = Static<typeof groupsSchema>
export const groupsValidator = getValidator(groupsSchema, dataValidator)
export const groupsResolver = resolve<Groups, HookContext>({})
export const groupsExternalResolver = resolve<Groups, HookContext>({})

// Schema for creating new data
export const groupsDataSchema = Type.Object({
  // No required fields for creation per original schema
  ...Type.Partial(Type.Omit(groupsSchema, ['_id'])).properties
}, { additionalProperties: false })

export type GroupsData = Static<typeof groupsDataSchema>
export const groupsDataValidator = getValidator(groupsDataSchema, dataValidator)
export const groupsDataResolver = resolve<GroupsData, HookContext>({})

// Schema for updating existing data
export const groupsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(groupsSchema, ['_id'])).properties,

  // MongoDB operators - matching original $addToSet/$pull for members
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $addToSet: Type.Optional(addToSet([
    { path: 'members', type: ObjectIdSchema() },
    { path: 'healthPlans', type: ObjectIdSchema() }
  ])),
  $pull: Type.Optional(pull([
    { path: 'members', type: ObjectIdSchema() },
    { path: 'healthPlans', type: ObjectIdSchema() }
  ]))
}, { additionalProperties: false })

export type GroupsPatch = Static<typeof groupsPatchSchema>
export const groupsPatchValidator = getValidator(groupsPatchSchema, dataValidator)
export const groupsPatchResolver = resolve<GroupsPatch, HookContext>({})

// Schema for allowed query properties - matching original schema
export const groupsQuerySchema = Type.Object({
  ...querySyntax(groupsSchema).properties
}, { additionalProperties: false })

export type GroupsQuery = Static<typeof groupsQuerySchema>
export const groupsQueryValidator = getValidator(groupsQuerySchema, queryValidator)
export const groupsQueryResolver = resolve<GroupsQuery, HookContext>({})
