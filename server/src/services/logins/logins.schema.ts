// TypeBox schema for logins service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, PhoneSchema } from '../../utils/common/typebox-schemas.js'

// Main data model schema
export const loginsSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Login identity fields from original schema
  name: Type.Optional(Type.String()),
  email: Type.Optional(Type.String()),
  phone: Type.Optional(Type.String()),
  password: Type.Optional(Type.String()),
  pendingPassword: Type.Optional(Type.String()),
  did: Type.Optional(Type.String()),
  ucan: Type.Optional(Type.String()),
  fingerprints: Type.Optional(Type.Array(ObjectIdSchema())),
  owner: Type.Optional(ObjectIdSchema()), // ppls

  // Key pair for cryptographic operations
  keyPair: Type.Optional(Type.Object({
    publicKey: Type.Optional(Type.String()),
    privateKey: Type.Optional(Type.String()),
    alg: Type.Optional(Type.String())
  })),

  // Social login IDs
  googleId: Type.Optional(Type.String()),
  facebookId: Type.Optional(Type.String()),
  twitterId: Type.Optional(Type.String()),
  linkedinId: Type.Optional(Type.String()),
  microsoftId: Type.Optional(Type.String()),
  githubId: Type.Optional(Type.String()),
  appleId: Type.Optional(Type.String()),

  // Verification and security
  isVerified: Type.Optional(Type.Boolean()),
  verifyToken: Type.Optional(Type.String()),
  verifyExpires: Type.Optional(Type.Any()),
  lastLogin: Type.Optional(Type.Any()),
  loginAttempts: Type.Optional(Type.Array(Type.Any())),
  locked: Type.Optional(Type.Any()),
  lastLoginMethod: Type.Optional(Type.String()),
  resetToken: Type.Optional(Type.String()),
  resetExpires: Type.Optional(Type.Any()),

  // Common fields
  ...commonFields.properties
}, { additionalProperties: true })

export type Logins = Static<typeof loginsSchema>
export const loginsValidator = getValidator(loginsSchema, dataValidator)
export const loginsResolver = resolve<Logins, HookContext>({})
export const loginsExternalResolver = resolve<Logins, HookContext>({
  // Security: Never expose sensitive fields externally
  password: async () => undefined,
  pendingPassword: async () => undefined,
  keyPair: async () => undefined,
  verifyToken: async () => undefined,
  resetToken: async () => undefined,
  loginAttempts: async () => undefined
})

// Schema for creating new data
export const loginsDataSchema = Type.Object({
  ...Type.Partial(Type.Omit(loginsSchema, ['_id'])).properties
}, { additionalProperties: true })

export type LoginsData = Static<typeof loginsDataSchema>
export const loginsDataValidator = getValidator(loginsDataSchema, dataValidator)
export const loginsDataResolver = resolve<LoginsData, HookContext>({})

// Schema for updating existing data
export const loginsPatchSchema = Type.Object({
  email: Type.Optional(Type.String()),
  password: Type.Optional(Type.String()),
  phone: Type.Optional(PhoneSchema),
  person: Type.Optional(ObjectIdSchema()),
  fingerprint: Type.Optional(ObjectIdSchema()),
  verified: Type.Optional(Type.Boolean()),
  emailVerified: Type.Optional(Type.Boolean()),
  phoneVerified: Type.Optional(Type.Boolean()),
  twoFactorEnabled: Type.Optional(Type.Boolean()),
  lastLogin: Type.Optional(Type.Any()),
  loginCount: Type.Optional(Type.Number()),
  failedAttempts: Type.Optional(Type.Number()),
  lockedUntil: Type.Optional(Type.Any()),
  resetToken: Type.Optional(Type.String()),
  resetTokenExpires: Type.Optional(Type.Any()),
  verificationToken: Type.Optional(Type.String()),
  verificationTokenExpires: Type.Optional(Type.Any()),
  roles: Type.Optional(Type.Array(Type.String())),
  permissions: Type.Optional(Type.Array(Type.String())),
  orgs: Type.Optional(Type.Array(ObjectIdSchema())),
  groups: Type.Optional(Type.Array(ObjectIdSchema())),
  teams: Type.Optional(Type.Array(ObjectIdSchema())),
  preferences: Type.Optional(Type.Record(Type.String(), Type.Any())),
  settings: Type.Optional(Type.Record(Type.String(), Type.Any())),
  active: Type.Optional(Type.Boolean()),
  suspended: Type.Optional(Type.Boolean()),
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $inc: Type.Optional(Type.Record(Type.String(), Type.Number())),
  $push: Type.Optional(Type.Object({
    roles: Type.Optional(Type.String()),
    permissions: Type.Optional(Type.String()),
    orgs: Type.Optional(ObjectIdSchema()),
    groups: Type.Optional(ObjectIdSchema()),
    teams: Type.Optional(ObjectIdSchema())
  }, { additionalProperties: false })),
  $pull: Type.Optional(Type.Object({
    roles: Type.Optional(Type.String()),
    permissions: Type.Optional(Type.String()),
    orgs: Type.Optional(ObjectIdSchema()),
    groups: Type.Optional(ObjectIdSchema()),
    teams: Type.Optional(ObjectIdSchema())
  }, { additionalProperties: false }))
}, { additionalProperties: true })

export type LoginsPatch = Static<typeof loginsPatchSchema>
export const loginsPatchValidator = getValidator(loginsPatchSchema, dataValidator)
export const loginsPatchResolver = resolve<LoginsPatch, HookContext>({})

// Schema for allowed query properties - matching original schema
export const loginsQuerySchema = Type.Object({
  ...querySyntax(loginsSchema).properties
}, { additionalProperties: true })

export type LoginsQuery = Static<typeof loginsQuerySchema>
export const loginsQueryValidator = getValidator(loginsQuerySchema, queryValidator)
export const loginsQueryResolver = resolve<LoginsQuery, HookContext>({})
