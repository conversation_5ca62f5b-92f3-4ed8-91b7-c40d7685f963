// TypeBox schema for refs service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, ImageSchema } from '../../utils/common/typebox-schemas.js'

export const refsSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Required fields from original schema
  name: Type.String(), // Required
  person: ObjectIdSchema(), // Required

  // Referral agent fields from original schema
  avatar: Type.Optional(ImageSchema),
  org: Type.Optional(ObjectIdSchema()),
  disabled: Type.Optional(Type.String()),
  disabledBy: Type.Optional(ObjectIdSchema()),
  disabledAt: Type.Optional(Type.Any()),
  isHost: Type.Optional(ObjectIdSchema()),
  showBy: Type.Optional(Type.Boolean()),
  npn: Type.Optional(Type.String()),
  sendTo: Type.Optional(Type.Any()), // sendTo schema
  approved: Type.Optional(Type.Boolean()),
  approvedAt: Type.Optional(Type.Any()),

  // Common fields
  ...commonFields.properties
}, { additionalProperties: true })

export type Refs = Static<typeof refsSchema>
export const refsValidator = getValidator(refsSchema, dataValidator)
export const refsResolver = resolve<Refs, HookContext>({})
export const refsExternalResolver = resolve<Refs, HookContext>({})

export const refsDataSchema = Type.Object({
  // Required fields for creation
  name: Type.String(),
  person: ObjectIdSchema(),

  // All other fields optional
  ...Type.Partial(Type.Omit(refsSchema, ['_id', 'name', 'person'])).properties
}, { additionalProperties: true })

export type RefsData = Static<typeof refsDataSchema>
export const refsDataValidator = getValidator(refsDataSchema, dataValidator)
export const refsDataResolver = resolve<RefsData, HookContext>({})

export const refsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(refsSchema, ['_id'])).properties,

  // MongoDB operators
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any()))
}, { additionalProperties: true })
export type RefsPatch = Static<typeof refsPatchSchema>
export const refsPatchValidator = getValidator(refsPatchSchema, dataValidator)
export const refsPatchResolver = resolve<RefsPatch, HookContext>({})

// Schema for allowed query properties - matching original schema
export const refsQuerySchema = Type.Object({
  ...querySyntax(refsSchema).properties
}, { additionalProperties: true })

export type RefsQuery = Static<typeof refsQuerySchema>
export const refsQueryValidator = getValidator(refsQuerySchema, queryValidator)
export const refsQueryResolver = resolve<RefsQuery, HookContext>({})
