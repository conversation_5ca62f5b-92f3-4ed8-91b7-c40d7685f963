// TypeBox schema for caps service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, addToSet, pull } from '../../utils/common/typebox-schemas.js'

export const capsSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Required fields from original schema
  subject: ObjectIdSchema(), // Required
  did: Type.String(), // Required

  // Optional fields from original schema
  subjectService: Type.Optional(Type.String()),

  // CRITICAL Pattern Property - restored correctly
  caps: Type.Optional(Type.Record(Type.String(), Type.Object({
    description: Type.Optional(Type.String()),
    ucan: Type.Optional(Type.String()),
    logins: Type.Optional(Type.Array(ObjectIdSchema()))
  }))), // Pattern: ^.*$

  // Common fields
  ...commonFields.properties
}, { additionalProperties: false })

export type Caps = Static<typeof capsSchema>
export const capsValidator = getValidator(capsSchema, dataValidator)
export const capsResolver = resolve<Caps, HookContext>({})
export const capsExternalResolver = resolve<Caps, HookContext>({})

export const capsDataSchema = Type.Object({
  // No required fields for creation per original schema
  ...Type.Partial(Type.Omit(capsSchema, ['_id'])).properties
}, { additionalProperties: false })

export type CapsData = Static<typeof capsDataSchema>
export const capsDataValidator = getValidator(capsDataSchema, dataValidator)
export const capsDataResolver = resolve<CapsData, HookContext>({})

export const capsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(capsSchema, ['_id'])).properties,

  // MongoDB operators - matching original schema
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $addToSet: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $pull: Type.Optional(Type.Record(Type.String(), Type.Any()))
}, { additionalProperties: false })
export type CapsPatch = Static<typeof capsPatchSchema>
export const capsPatchValidator = getValidator(capsPatchSchema, dataValidator)
export const capsPatchResolver = resolve<CapsPatch, HookContext>({})

// Schema for allowed query properties - matching original schema
export const capsQuerySchema = Type.Object({
  ...querySyntax(capsSchema).properties
}, { additionalProperties: false })

export type CapsQuery = Static<typeof capsQuerySchema>
export const capsQueryValidator = getValidator(capsQuerySchema, queryValidator)
export const capsQueryResolver = resolve<CapsQuery, HookContext>({})
