// TypeBox schema for ppls service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, PhoneSchema, ServiceAddressSchema, addToSet, pull } from '../../utils/common/typebox-schemas.js'

export const pplsSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Basic person info
  name: Type.Optional(Type.String()),
  lastName: Type.Optional(Type.String()),
  firstName: Type.Optional(Type.String()),
  did: Type.Optional(Type.String()),
  fid: Type.Optional(Type.String()),
  wallet: Type.Optional(ObjectIdSchema()),

  // Contact info
  address: Type.Optional(ServiceAddressSchema),
  addresses: Type.Optional(Type.Array(ServiceAddressSchema)),
  email: Type.Optional(Type.Union([Type.String(), Type.Null()])),
  emails: Type.Optional(Type.Array(Type.String())),
  phone: Type.Optional(Type.Union([Type.Null(), PhoneSchema])),
  phones: Type.Optional(Type.Array(PhoneSchema)),

  // Relationships
  household: Type.Optional(ObjectIdSchema()),
  login: Type.Optional(ObjectIdSchema()),

  // Profile
  avatar: Type.Optional(Type.Any()), // ImageSchema
  online: Type.Optional(Type.Boolean()),
  onlineAt: Type.Optional(Type.Any()),

  // Financial accounts
  ramp_user_id: Type.Optional(Type.String()),
  moovCardholderId: Type.Optional(Type.String()),

  // Arrays
  ims: Type.Optional(Type.Array(ObjectIdSchema())),
  cams: Type.Optional(Type.Array(ObjectIdSchema())),
  inGroups: Type.Optional(Type.Array(ObjectIdSchema())),
  inOrgs: Type.Optional(Type.Array(ObjectIdSchema())),
  refs: Type.Optional(Type.Array(ObjectIdSchema())),
  badges: Type.Optional(Type.Array(Type.String())),
  enrollments: Type.Optional(Type.Array(ObjectIdSchema())),
  card_user: Type.Optional(Type.Array(ObjectIdSchema())),
  budget_user: Type.Optional(Type.Array(ObjectIdSchema())),

  // Personal data
  dob: Type.Optional(Type.String()),
  ssn: Type.Optional(Type.String()),
  itin: Type.Optional(Type.String()),
  last4Itin: Type.Optional(Type.String()),
  last4Ssn: Type.Optional(Type.String()),
  gender: Type.Optional(Type.String()),
  cleanupFlag: Type.Optional(Type.Boolean()),
  lastGroupSync: Type.Optional(Type.Any()),

  // CRITICAL Pattern Properties (dynamic keys) - These were completely lost!
  moovAccounts: Type.Optional(Type.Record(Type.String(), Type.Object({
    id: Type.String(),
    isController: Type.Boolean()
  }))), // Pattern: ^.*$

  invites: Type.Optional(Type.Record(Type.String(), Type.Object({
    by: ObjectIdSchema(),
    at:Type.Optional(Type.Any()),
    reminded: Type.Array(Type.Any()),
    caps: Type.Record(Type.String(), Type.Object({
      id: ObjectIdSchema(),
      path: Type.String()
    }))
  }))), // Pattern: ^.*$ with nested pattern property for caps

  // Preferences object
  preferences: Type.Optional(Type.Object({
    notifications: Type.Optional(Type.Object({
      unsub: Type.Optional(Type.Array(Type.String())),
      emailUnsub: Type.Optional(Type.Array(Type.String())),
      smsUnsub: Type.Optional(Type.Array(Type.String()))
    }))
  })),

  // Common fields
  ...commonFields.properties
}, { additionalProperties: false })

export type Ppls = Static<typeof pplsSchema>
export const pplsValidator = getValidator(pplsSchema, dataValidator)
export const pplsResolver = resolve<Ppls, HookContext>({
  // Trim handlers from original schema
  firstName: async (val) => val?.trim?.() || val,
  lastName: async (val) => val?.trim?.() || val,
  name: async (val) => val?.trim?.() || val
})
export const pplsExternalResolver = resolve<Ppls, HookContext>({})

export const pplsDataSchema = Type.Object({
  ...Type.Omit(pplsSchema, ['_id']).properties
}, { additionalProperties: false })

export type PplsData = Static<typeof pplsDataSchema>
export const pplsDataValidator = getValidator(pplsDataSchema, dataValidator)
export const pplsDataResolver = resolve<PplsData, HookContext>({})

export const pplsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(pplsSchema, ['_id'])).properties,
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $addToSet: Type.Optional(addToSet([
    { path: 'inGroups', type: ObjectIdSchema() },
    { path: 'card_user', type: ObjectIdSchema() },
    { path: 'budget_user', type: ObjectIdSchema() },
    { path: 'inOrgs', type: ObjectIdSchema() },
    { path: 'cams', type: ObjectIdSchema() },
    { path: 'ims', type: ObjectIdSchema() },
    { path: 'refs', type: ObjectIdSchema() },
    { path: 'badges', type: Type.String() },
    { path: 'enrollments', type: ObjectIdSchema() },
    { path: 'phones', type: PhoneSchema },
    { path: 'emails', type: Type.String() },
    { path: 'addresses', type: ServiceAddressSchema }
  ])),
  $pull: Type.Optional(pull([
    { path: 'inGroups', type: ObjectIdSchema() },
    { path: 'card_user', type: ObjectIdSchema() },
    { path: 'budget_user', type: ObjectIdSchema() },
    { path: 'inOrgs', type: ObjectIdSchema() },
    { path: 'cams', type: ObjectIdSchema() },
    { path: 'ims', type: ObjectIdSchema() },
    { path: 'refs', type: ObjectIdSchema() },
    { path: 'badges', type: Type.String() },
    { path: 'enrollments', type: ObjectIdSchema() },
    { path: 'phones', type: PhoneSchema },
    { path: 'emails', type: Type.String() },
    { path: 'addresses', type: ServiceAddressSchema }
  ]))
}, { additionalProperties: false })
export type PplsPatch = Static<typeof pplsPatchSchema>
export const pplsPatchValidator = getValidator(pplsPatchSchema, dataValidator)
export const pplsPatchResolver = resolve<PplsPatch, HookContext>({})

// Schema for allowed query properties with custom query fields
export const pplsQuerySchema = Type.Object({
  ...querySyntax(Type.Object({
    ...pplsSchema.properties,
    // Custom query properties from original schema
    name:Type.Optional(Type.Any()),
    email:Type.Optional(Type.Any()),
    phone:Type.Optional(Type.Any()),
    firstName:Type.Optional(Type.Any()),
    lastName:Type.Optional(Type.Any())
  })).properties,

  // Complex query operators for specific fields
  cams: Type.Optional(Type.Union([
    Type.Array(ObjectIdSchema()),
    Type.Object({
      $exists: Type.Boolean()
    })
  ])),

  login: Type.Optional(Type.Union([
    ObjectIdSchema(),
    Type.Object({
      $exists: Type.Boolean()
    })
  ])),

  plan_search: Type.Optional(Type.Object({
    id: ObjectIdSchema(),
    memberOnly: Type.Optional(Type.Boolean())
  }))
}, { additionalProperties: true })

export type PplsQuery = Static<typeof pplsQuerySchema>
export const pplsQueryValidator = getValidator(pplsQuerySchema, queryValidator)
export const pplsQueryResolver = resolve<PplsQuery, HookContext>({})
