// TypeBox schema for prices service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, addToSet, pull } from '../../utils/common/typebox-schemas.js'

export const pricesSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Healthcare price fields from original schema
  provider: Type.Optional(ObjectIdSchema()),
  bundle: Type.Optional(ObjectIdSchema()),
  source: Type.Optional(Type.Union([
    Type.Literal('vision'), Type.Literal('bill'), Type.Literal('provider'),
    Type.Literal('dataset'), Type.Literal('va'), Type.Literal('upload'), Type.Literal('ptf')
  ])),
  description: Type.Optional(Type.String()),
  notes: Type.Optional(Type.String()),
  session: Type.Optional(Type.String()),
  state: Type.Optional(Type.String()), // Two letter US state code
  eraser: Type.Optional(ObjectIdSchema()),
  name: Type.Optional(Type.String()),
  providerName: Type.Optional(Type.String()),
  price: Type.Optional(Type.Number()),
  uom: Type.Optional(Type.String()),
  batch: Type.Optional(Type.String()),
  uid: Type.Optional(Type.String()),
  subject: Type.Optional(ObjectIdSchema()),
  type: Type.Optional(Type.Union([
    Type.Literal('procedures'), Type.Literal('meds'), Type.Literal('other')
  ])),
  code: Type.Optional(Type.String()),
  billing_code: Type.Optional(Type.String()),
  carrier: Type.Optional(Type.String()),
  ndcs: Type.Optional(Type.Array(Type.String())),
  relatedCheckedAt: Type.Optional(Type.Any()),
  ndc: Type.Optional(Type.String()),
  ndc10: Type.Optional(Type.String()),
  ndc11: Type.Optional(Type.String()),
  labeler: Type.Optional(Type.String()), // First 6 NDC 11
  product: Type.Optional(Type.String()), // 4 digit product code
  package: Type.Optional(Type.String()), // 2 digit package code
  rxcui: Type.Optional(Type.String()),
  s_f: Type.Optional(Type.String()),

  // Common fields
  ...commonFields.properties
}, { additionalProperties: false })

export type Prices = Static<typeof pricesSchema>
export const pricesValidator = getValidator(pricesSchema, dataValidator)
export const pricesResolver = resolve<Prices, HookContext>({
  // Type resolver from original schema
  type: async (val) => {
    if (!val) return 'procedures'
    return val
  }
})
export const pricesExternalResolver = resolve<Prices, HookContext>({})

export const pricesDataSchema = Type.Object({
  // No required fields for creation per original schema
  ...Type.Partial(Type.Omit(pricesSchema, ['_id'])).properties
}, { additionalProperties: false })

export type PricesData = Static<typeof pricesDataSchema>
export const pricesDataValidator = getValidator(pricesDataSchema, dataValidator)
export const pricesDataResolver = resolve<PricesData, HookContext>({})

export const pricesPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(pricesSchema, ['_id'])).properties,

  // MongoDB operators - matching original pushPull for ndcs
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $addToSet: Type.Optional(addToSet([
    { path: 'ndcs', type: Type.String() }
  ])),
  $push: Type.Optional(addToSet([
    { path: 'ndcs', type: Type.String() }
  ])),
  $pull: Type.Optional(pull([
    { path: 'ndcs', type: Type.String() }
  ]))
}, { additionalProperties: false })
export type PricesPatch = Static<typeof pricesPatchSchema>
export const pricesPatchValidator = getValidator(pricesPatchSchema, dataValidator)
export const pricesPatchResolver = resolve<PricesPatch, HookContext>({})

// Schema for allowed query properties - matching original schema with custom properties
export const pricesQuerySchema = Type.Object({
  ...querySyntax(pricesSchema).properties,

  // Custom query properties from original schema
  name: Type.Optional(Type.Any()),
  description: Type.Optional(Type.Any()),
  s_f: Type.Optional(Type.Any()),
  price: Type.Optional(Type.Any())
}, { additionalProperties: true })

export type PricesQuery = Static<typeof pricesQuerySchema>
export const pricesQueryValidator = getValidator(pricesQuerySchema, queryValidator)
export const pricesQueryResolver = resolve<PricesQuery, HookContext>({})
