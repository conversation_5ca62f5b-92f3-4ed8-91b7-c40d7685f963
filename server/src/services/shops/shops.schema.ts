// TypeBox schema for shops service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, pull } from '../../utils/common/typebox-schemas.js'

// Simplified schemas (would need full imports in real implementation)
const MandateSchema = Type.Object({
  // Simplified mandate fields
  agreed: Type.Optional(Type.Boolean()),
  at: Type.Optional(Type.Any())
}, { additionalProperties: true })

const VectorStoreSchema = Type.Object({
  // Simplified vector store fields
  id: Type.Optional(Type.String())
}, { additionalProperties: true })

const PeopleSchema = Type.Object({
  // Simplified people schema
  id: Type.Optional(ObjectIdSchema())
}, { additionalProperties: true })

const PlaceSchema = Type.Object({
  // Simplified place schema
  city: Type.Optional(Type.String()),
  state: Type.Optional(Type.String())
}, { additionalProperties: true })

const DistSchema = Type.Object({
  // Simplified distribution schema
  data: Type.Optional(Type.Array(Type.Number()))
}, { additionalProperties: true })

const ScoresSchema = Type.Object({
  // Simplified scores schema
  score: Type.Optional(Type.Number())
}, { additionalProperties: true })

export const shopsSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Healthcare shopping fields from original schema
  person: Type.Optional(ObjectIdSchema()),
  plan: Type.Optional(ObjectIdSchema()),
  enrollment: Type.Optional(ObjectIdSchema()),
  plan_coverage: Type.Optional(ObjectIdSchema()),
  aiChatCount: Type.Optional(Type.Number()),
  vectorStore: Type.Optional(VectorStoreSchema),
  resetId: Type.Optional(Type.String()),
  limit_remaining: Type.Optional(Type.Number()),
  tax_rate: Type.Optional(Type.Number()),
  planYear: Type.Optional(Type.String()),
  version: Type.Optional(Type.String()),
  consent_to_changes: Type.Optional(MandateSchema),
  decline_changes: Type.Optional(MandateSchema),

  // Stats object with complex structure
  stats: Type.Optional(Type.Object({
    peopleEdited: Type.Optional(Type.Any()),
    inactive: Type.Optional(Type.Boolean()),
    people: Type.Optional(Type.Array(PeopleSchema)),
    gender: Type.Optional(Type.Union([Type.Literal('male'), Type.Literal('female')])),
    preEx: Type.Optional(Type.Boolean()),
    income: Type.Optional(Type.Number()),
    age: Type.Optional(Type.Number()),
    spend: Type.Optional(Type.Number()),
    ded: Type.Optional(Type.Number()),
    spouse: Type.Optional(Type.Boolean()),
    plus: Type.Optional(Type.Number()),
    household_size: Type.Optional(Type.Number()),
    smoker: Type.Optional(Type.Boolean()),
    risk: Type.Optional(Type.Number()),
    city: Type.Optional(Type.String()),
    place: Type.Optional(PlaceSchema)
  })),

  // Shopping calculation fields
  mult: Type.Optional(Type.Number()),
  useAptc: Type.Optional(Type.Boolean()),
  skipAptc: Type.Optional(Type.Boolean()),
  compare_ids: Type.Optional(Type.Array(Type.String())),
  allSpend: Type.Optional(Type.Number()),
  byYear: Type.Optional(Type.Array(Type.Number())),
  issuers: Type.Optional(Type.Array(Type.String())),
  aca_issuers: Type.Optional(Type.Array(Type.String())),
  lastRun: Type.Optional(Type.Any()),
  worst10: Type.Optional(Type.Array(Type.Number())),
  distribution: Type.Optional(DistSchema),
  distribution_ptc: Type.Optional(DistSchema),
  aptc: Type.Optional(Type.Number()),
  slcsp: Type.Optional(Type.Number()),
  coverage_scores: Type.Optional(ScoresSchema),
  coverage_scores_ptc: Type.Optional(ScoresSchema),
  spend: Type.Optional(Type.Number()),
  simsCount: Type.Optional(Type.Number()),
  coverages: Type.Optional(Type.Array(Type.Any())),

  // Shopping workflow fields
  stage: Type.Optional(Type.Union([
    Type.Literal('shopping'), Type.Literal('review'), Type.Literal('sign'),
    Type.Literal('complete'), Type.Literal('closed')
  ])),
  status: Type.Optional(Type.String()),
  policy: Type.Optional(Type.String()),
  coverage: Type.Optional(ObjectIdSchema()),
  team: Type.Optional(ObjectIdSchema()),
  gps: Type.Optional(ObjectIdSchema()),
  majorMedical: Type.Optional(Type.String()),
  keepOld: Type.Optional(Type.Boolean()),
  cashInLieu: Type.Optional(Type.Boolean()),
  deduction: Type.Optional(Type.Number()),
  interval: Type.Optional(Type.String()),
  lastPremium: Type.Optional(Type.Number()),
  comments: Type.Optional(Type.String()),
  fid: Type.Optional(Type.String()),
  form: Type.Optional(ObjectIdSchema()),
  anon: Type.Optional(Type.Boolean()),
  jobTitle: Type.Optional(Type.String()),
  name: Type.Optional(Type.String()),
  email: Type.Optional(Type.String()),

  // CRITICAL Pattern Properties - All 3 were completely lost!
  attest: Type.Optional(Type.Record(Type.String(), MandateSchema)), // Pattern: ^.*$

  spend_dist: Type.Optional(Type.Record(Type.String(), Type.Object({
    spend: Type.Optional(Type.Number()),
    count: Type.Optional(Type.Number())
  }))), // Pattern: ^.*$

  choices: Type.Optional(Type.Record(Type.String(), ObjectIdSchema())), // Pattern: ^.*$

  // Common fields
  ...commonFields.properties
}, { additionalProperties: false })

export type Shops = Static<typeof shopsSchema>
export const shopsValidator = getValidator(shopsSchema, dataValidator)
export const shopsResolver = resolve<Shops, HookContext>({
  // Resolvers from original schema
  stage: async (val) => {
    return val || 'shopping'
  },
  mult: async (val) => {
    if (!val) return 12
    return val
  }
})
export const shopsExternalResolver = resolve<Shops, HookContext>({})

export const shopsDataSchema = Type.Object({
  // No required fields for creation per original schema
  ...Type.Partial(Type.Omit(shopsSchema, ['_id'])).properties
}, { additionalProperties: false })

export type ShopsData = Static<typeof shopsDataSchema>
export const shopsDataValidator = getValidator(shopsDataSchema, dataValidator)
export const shopsDataResolver = resolve<ShopsData, HookContext>({})

export const shopsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(shopsSchema, ['_id'])).properties,

  // MongoDB operators - matching original pushPull for coverages
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $pull: Type.Optional(pull([
    { path: 'coverages', type: ObjectIdSchema() }
  ]))
}, { additionalProperties: false })
export type ShopsPatch = Static<typeof shopsPatchSchema>
export const shopsPatchValidator = getValidator(shopsPatchSchema, dataValidator)
export const shopsPatchResolver = resolve<ShopsPatch, HookContext>({})

// Schema for allowed query properties - matching original schema
export const shopsQuerySchema = Type.Object({
  ...querySyntax(shopsSchema).properties
}, { additionalProperties: false })

export type ShopsQuery = Static<typeof shopsQuerySchema>
export const shopsQueryValidator = getValidator(shopsQuerySchema, queryValidator)
export const shopsQueryResolver = resolve<ShopsQuery, HookContext>({})
