// TypeBox schema for wallets service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

export const walletsSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Required fields from original schema
  owner: ObjectIdSchema(), // Required
  ownerService: Type.Union([Type.Literal('ppls'), Type.Literal('orgs')]), // Required enum

  // CRITICAL Pattern Property - restored correctly
  methods: Type.Optional(Type.Record(Type.String(), Type.Object({
    id: Type.String(),
    name: Type.Optional(Type.String())
  }))), // Pattern: ^.*$

  // Common fields
  ...commonFields.properties
}, { additionalProperties: false })

export type Wallets = Static<typeof walletsSchema>
export const walletsValidator = getValidator(walletsSchema, dataValidator)
export const walletsResolver = resolve<Wallets, HookContext>({})
export const walletsExternalResolver = resolve<Wallets, HookContext>({})

export const walletsDataSchema = Type.Object({
  // Required fields for creating new wallets
  owner: ObjectIdSchema(),

  // All other fields from main schema (optional for creation)
  ...Type.Partial(Type.Omit(walletsSchema, ['_id', 'owner'])).properties
}, { additionalProperties: false })

export type WalletsData = Static<typeof walletsDataSchema>
export const walletsDataValidator = getValidator(walletsDataSchema, dataValidator)
export const walletsDataResolver = resolve<WalletsData, HookContext>({})

export const walletsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(walletsSchema, ['_id'])).properties,

  // MongoDB operators
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any()))
}, { additionalProperties: false })
export type WalletsPatch = Static<typeof walletsPatchSchema>
export const walletsPatchValidator = getValidator(walletsPatchSchema, dataValidator)
export const walletsPatchResolver = resolve<WalletsPatch, HookContext>({})

// Schema for allowed query properties - matching original schema
export const walletsQuerySchema = Type.Object({
  ...querySyntax(walletsSchema).properties
}, { additionalProperties: false })

export type WalletsQuery = Static<typeof walletsQuerySchema>
export const walletsQueryValidator = getValidator(walletsQuerySchema, queryValidator)
export const walletsQueryResolver = resolve<WalletsQuery, HookContext>({})
