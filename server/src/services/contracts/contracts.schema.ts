// TypeBox schema for contracts service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, MandateSchema } from '../../utils/common/typebox-schemas.js'

// Updates schema for contract rejections
const UpdatesSchema = Type.Object({
  by: Type.Optional(ObjectIdSchema()),
  at: Type.Optional(Type.Any()),
  reason: Type.Optional(Type.String())
}, { additionalProperties: false })

// Pay schema for contract payments
const PaySchema = Type.Object({
  amount: Type.Optional(Type.Number()),
  currency: Type.Optional(Type.String()),
  schedule: Type.Optional(Type.String())
}, { additionalProperties: false })

// Sections schema for contract sections
const SectionsSchema = Type.Object({
  title: Type.Optional(Type.String()),
  content: Type.Optional(Type.String()),
  order: Type.Optional(Type.Number())
}, { additionalProperties: false })

export const contractsSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Required fields from original schema
  name: Type.String(), // Required

  // Contract configuration
  public: Type.Optional(Type.Boolean()),
  template: Type.Optional(Type.Boolean()),
  subject: Type.Optional(ObjectIdSchema()),
  subjectService: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  owner: Type.Optional(ObjectIdSchema()),
  ownerService: Type.Optional(Type.String()),
  managers: Type.Optional(Type.Array(ObjectIdSchema())),
  tags: Type.Optional(Type.Array(Type.String())),
  status: Type.Optional(Type.Union([
    Type.Literal('open'), Type.Literal('sent'),
    Type.Literal('rejected'), Type.Literal('executed')
  ])),
  rejectedBy: Type.Optional(UpdatesSchema),
  pTags: Type.Optional(Type.Array(Type.String())),

  // CRITICAL Pattern Property - This was completely lost!
  parties: Type.Optional(Type.Record(Type.String(), Type.Object({
    relationship: Type.Optional(Type.String()),
    by: Type.Optional(Type.String()),
    byTitle: Type.Optional(Type.String()),
    id: Type.Optional(ObjectIdSchema()),
    idService: Type.Optional(Type.String()),
    aka: Type.Optional(Type.String()),
    tag: Type.Optional(Type.String()),
    legalName: Type.Optional(Type.String()),
    address: Type.Optional(Type.String()),
    email: Type.Optional(Type.String()),
    phone: Type.Optional(Type.String()),
    ack: Type.Optional(MandateSchema)
  }))), // Pattern: ^.*$

  // Complex objects
  meta: Type.Optional(Type.Object({
    pay: Type.Optional(PaySchema)
  }, { additionalProperties: true })),
  sections: Type.Optional(SectionsSchema),

  // Common fields
  ...commonFields.properties
}, { additionalProperties: false })

export type Contracts = Static<typeof contractsSchema>
export const contractsValidator = getValidator(contractsSchema, dataValidator)
export const contractsResolver = resolve<Contracts, HookContext>({
  // Default status resolver from original schema
  status: async (val) => {
    if (!val) return 'open'
    return val
  }
})
export const contractsExternalResolver = resolve<Contracts, HookContext>({})

export const contractsDataSchema = Type.Object({
  // Required fields for creating new contracts
  name: Type.String(),

  // All other fields from main schema (optional for creation)
  ...Type.Partial(Type.Omit(contractsSchema, ['_id', 'name'])).properties
}, { additionalProperties: false })

export type ContractsData = Static<typeof contractsDataSchema>
export const contractsDataValidator = getValidator(contractsDataSchema, dataValidator)
export const contractsDataResolver = resolve<ContractsData, HookContext>({})

export const contractsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(contractsSchema, ['_id'])).properties,

  // MongoDB operators
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any()))
}, { additionalProperties: false })
export type ContractsPatch = Static<typeof contractsPatchSchema>
export const contractsPatchValidator = getValidator(contractsPatchSchema, dataValidator)
export const contractsPatchResolver = resolve<ContractsPatch, HookContext>({})

// Schema for allowed query properties - matching original schema
export const contractsQuerySchema = Type.Object({
  ...querySyntax(contractsSchema).properties
}, { additionalProperties: false })

export type ContractsQuery = Static<typeof contractsQuerySchema>
export const contractsQueryValidator = getValidator(contractsQuerySchema, queryValidator)
export const contractsQueryResolver = resolve<ContractsQuery, HookContext>({})
