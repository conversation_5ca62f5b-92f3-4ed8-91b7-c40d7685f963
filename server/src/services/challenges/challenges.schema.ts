// TypeBox schema for challenges service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

// Challenge kinds enum from passkeys service
const challengeKinds = ['registration', 'authentication'] as const

export const challengesSchema = Type.Object({
  _id: ObjectIdSchema(),

  // WebAuthn challenge fields
  kind: Type.Optional(Type.Union([Type.Literal('registration'), Type.Literal('authentication')])),
  login: Type.Optional(ObjectIdSchema()), // may be absent for usernameless auth
  challenge: Type.Optional(Type.String()), // Opaque WebAuthn challenge (base64url or raw) - NEVER expose externally
  connectionId: Type.Optional(Type.String()), // Feathers socket connection id
  expiresAt: Type.Optional(Type.Number()), // epoch ms; short TTL (e.g., 2–5 minutes)

  // Common fields
  ...commonFields.properties
}, { additionalProperties: false })

export type Challenges = Static<typeof challengesSchema>
export const challengesValidator = getValidator(challengesSchema, dataValidator)
export const challengesResolver = resolve<Challenges, HookContext>({})
export const challengesExternalResolver = resolve<Challenges, HookContext>({
  // Security: Never expose challenge field externally
  challenge: async () => undefined
})

export const challengesDataSchema = Type.Object({
  ...Type.Omit(challengesSchema, ['_id']).properties
}, { additionalProperties: false })

export type ChallengesData = Static<typeof challengesDataSchema>
export const challengesDataValidator = getValidator(challengesDataSchema, dataValidator)
export const challengesDataResolver = resolve<ChallengesData, HookContext>({})

export const challengesPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(challengesSchema, ['_id'])).properties,

  // MongoDB operators
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any()))
}, { additionalProperties: false })
export type ChallengesPatch = Static<typeof challengesPatchSchema>
export const challengesPatchValidator = getValidator(challengesPatchSchema, dataValidator)
export const challengesPatchResolver = resolve<ChallengesPatch, HookContext>({})

// Schema for allowed query properties - matching original schema
export const challengesQuerySchema = Type.Object({
  ...querySyntax(challengesSchema).properties
}, { additionalProperties: false })

export type ChallengesQuery = Static<typeof challengesQuerySchema>
export const challengesQueryValidator = getValidator(challengesQuerySchema, queryValidator)
export const challengesQueryResolver = resolve<ChallengesQuery, HookContext>({})
