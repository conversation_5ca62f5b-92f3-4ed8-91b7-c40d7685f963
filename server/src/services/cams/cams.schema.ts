// TypeBox schema for cams service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'
import {compsCamsSchema, compsInterval} from '../comps/comps.schema.js';

// required: ['_id', 'org', 'person'],
//     properties: {
//   _id: ObjectIdSchema(),
//       person: ObjectIdSchema(),
//       comp: ObjectIdSchema(),
//       hireDate: {},
//   hoursWorked: { type: 'number' },
//   off: { type: 'boolean' },
//   stage: { type: 'string' },
//   active: { type: 'string' },
//   group: ObjectIdSchema(),
//       terminated: { type: 'boolean' },
//   terminatedAt: {},
//   terminatedBy: ObjectIdSchema(),
// ...compsCamsSchema.properties,
// ...commonFields.properties
// }


export const camsSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Required fields from original schema
  person: ObjectIdSchema(), // Required
  amount: Type.Optional(Type.Number()),
  interval: Type.Optional(compsInterval),
  // CAM-specific fields
  comp: Type.Optional(ObjectIdSchema()),
  hireDate: Type.Optional(Type.Any()),
  hoursWorked: Type.Optional(Type.Number()),
  off: Type.Optional(Type.Boolean()),
  stage: Type.Optional(Type.String()),
  active: Type.Optional(Type.String()), // String, not boolean
  group: Type.Optional(ObjectIdSchema()),
  terminated: Type.Optional(Type.Boolean()),
  terminatedAt: Type.Optional(Type.Any()),
  terminatedBy: Type.Optional(ObjectIdSchema()),

  // Fields from compsCamsSchema

  ...compsCamsSchema,

  // Common fields
  ...commonFields.properties
}, { additionalProperties: false })

export type Cams = Static<typeof camsSchema>
export const camsValidator = getValidator(camsSchema, dataValidator)
export const camsResolver = resolve<Cams, HookContext>({})
export const camsExternalResolver = resolve<Cams, HookContext>({})

export const camsDataSchema = Type.Object({
  // Required fields for creating new CAMs
  person: ObjectIdSchema(),
  org: ObjectIdSchema(),

  // All other fields from main schema (optional for creation)
  ...Type.Partial(Type.Omit(camsSchema, ['_id', 'person', 'org'])).properties
}, { additionalProperties: false })

export type CamsData = Static<typeof camsDataSchema>
export const camsDataValidator = getValidator(camsDataSchema, dataValidator)
export const camsDataResolver = resolve<CamsData, HookContext>({})

export const camsPatchSchema = Type.Object({
  // All schema properties (for direct field updates)
  ...Type.Omit(camsSchema, ['_id']).properties,

  // Partial schema properties (for optional updates)
  ...Type.Partial(Type.Omit(camsSchema, ['_id'])).properties,

  // MongoDB operators - matching original $inc: {}
  $inc: Type.Optional(Type.Record(Type.String(), Type.Number())),
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any()))
}, { additionalProperties: false })
export type CamsPatch = Static<typeof camsPatchSchema>
export const camsPatchValidator = getValidator(camsPatchSchema, dataValidator)
export const camsPatchResolver = resolve<CamsPatch, HookContext>({})

// Schema for allowed query properties - matching original schema
export const camsQuerySchema = Type.Object({
  ...querySyntax(camsSchema).properties,

  // Custom query properties from original schema
  name: Type.Optional(Type.Any()),

  // Exists queries for extras and comp fields
  extras: Type.Optional(Type.Union([
    Type.Record(Type.String(), Type.Any()),
    Type.Object({
      $exists: Type.Boolean()
    })
  ])),

  comp: Type.Optional(Type.Union([
    ObjectIdSchema(),
    Type.Object({
      $exists: Type.Boolean()
    })
  ]))
}, { additionalProperties: false })

export type CamsQuery = Static<typeof camsQuerySchema>
export const camsQueryValidator = getValidator(camsQuerySchema, queryValidator)
export const camsQueryResolver = resolve<CamsQuery, HookContext>({})
