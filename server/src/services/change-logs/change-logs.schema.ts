// TypeBox schema for change-logs service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

export const changeLogsSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Required fields from original schema
  recordId: ObjectIdSchema(), // Required
  service: Type.String(), // Required

  // Dynamic field history tracking - additionalProperties: true allows any field with history structure
  // Pattern: [key:string]: { history: [{ data: oldVal, updatedAt: Date, updatedBy: { login: id } }] }

  // Common fields
  ...commonFields.properties
}, { additionalProperties: true })

export type ChangeLogs = Static<typeof changeLogsSchema>
export const changeLogsValidator = getValidator(changeLogsSchema, dataValidator)
export const changeLogsResolver = resolve<ChangeLogs, HookContext>({})
export const changeLogsExternalResolver = resolve<ChangeLogs, HookContext>({})

export const changeLogsDataSchema = Type.Object({
  // Required fields for creating new change logs (no required fields per original)
  ...Type.Partial(Type.Omit(changeLogsSchema, ['_id'])).properties
}, { additionalProperties: true })

export type ChangeLogsData = Static<typeof changeLogsDataSchema>
export const changeLogsDataValidator = getValidator(changeLogsDataSchema, dataValidator)
export const changeLogsDataResolver = resolve<ChangeLogsData, HookContext>({})

export const changeLogsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(changeLogsSchema, ['_id'])).properties,

  // MongoDB operators
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any()))
}, { additionalProperties: true })
export type ChangeLogsPatch = Static<typeof changeLogsPatchSchema>
export const changeLogsPatchValidator = getValidator(changeLogsPatchSchema, dataValidator)
export const changeLogsPatchResolver = resolve<ChangeLogsPatch, HookContext>({})

// Schema for allowed query properties - matching original schema
export const changeLogsQuerySchema = Type.Object({
  ...querySyntax(changeLogsSchema).properties
}, { additionalProperties: true })

export type ChangeLogsQuery = Static<typeof changeLogsQuerySchema>
export const changeLogsQueryValidator = getValidator(changeLogsQuerySchema, queryValidator)
export const changeLogsQueryResolver = resolve<ChangeLogsQuery, HookContext>({})
