// TypeBox schema for mbrs service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, ServiceAddressSchema, PhoneSchema } from '../../utils/common/typebox-schemas.js'

// Main data model schema
export const mbrsSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Required fields from original schema
  person: ObjectIdSchema(), // Required
  coverage: ObjectIdSchema(), // Required
  itemId: Type.String(), // Required

  // Membership tracking fields from original schema
  enrollment: Type.Optional(ObjectIdSchema()),
  plan: Type.Optional(ObjectIdSchema()),
  provider: Type.Optional(ObjectIdSchema()),
  pm: Type.Optional(ObjectIdSchema()), // Plan manager
  inactive: Type.Optional(Type.Boolean()),

  // Common fields
  ...commonFields.properties
}, { additionalProperties: false })

export type Mbrs = Static<typeof mbrsSchema>
export const mbrsValidator = getValidator(mbrsSchema, dataValidator)
export const mbrsResolver = resolve<Mbrs, HookContext>({})
export const mbrsExternalResolver = resolve<Mbrs, HookContext>({})

// Schema for creating new data
export const mbrsDataSchema = Type.Object({
  // Required fields for creating new mbrs
  person: ObjectIdSchema(),
  coverage: ObjectIdSchema(),
  itemId: Type.String(),

  // All other fields from main schema (optional for creation)
  ...Type.Partial(Type.Omit(mbrsSchema, ['_id', 'person', 'coverage', 'itemId'])).properties
}, { additionalProperties: false })

export type MbrsData = Static<typeof mbrsDataSchema>
export const mbrsDataValidator = getValidator(mbrsDataSchema, dataValidator)
export const mbrsDataResolver = resolve<MbrsData, HookContext>({})

// Schema for updating existing data
export const mbrsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(mbrsSchema, ['_id'])).properties,

  // MongoDB operators
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any()))
}, { additionalProperties: false })

export type MbrsPatch = Static<typeof mbrsPatchSchema>
export const mbrsPatchValidator = getValidator(mbrsPatchSchema, dataValidator)
export const mbrsPatchResolver = resolve<MbrsPatch, HookContext>({})

// Schema for allowed query properties - matching original schema
export const mbrsQuerySchema = Type.Object({
  ...querySyntax(mbrsSchema).properties
}, { additionalProperties: false })

export type MbrsQuery = Static<typeof mbrsQuerySchema>
export const mbrsQueryValidator = getValidator(mbrsQuerySchema, queryValidator)
export const mbrsQueryResolver = resolve<MbrsQuery, HookContext>({})
