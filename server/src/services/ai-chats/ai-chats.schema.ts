// TypeBox schema for ai-chats service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

// Chat history item schema
const ChatHistoryItemSchema = Type.Object({
  session: Type.Optional(Type.String()),
  createdAt: Type.Optional(Type.Any()),
  subject: Type.Optional(Type.Union([
    Type.Literal('plan_docs'),
    Type.Literal('contracts'),
    Type.Literal('coverages'),
    Type.Literal('medical'),
    Type.Literal('shops')
  ])),
  question: Type.Optional(Type.String()),
  annotations: Type.Optional(Type.Array(Type.Any())),
  answer: Type.Optional(Type.String())
}, { additionalProperties: false })

// Main data model schema - matching original required fields
export const aiChatsSchema = Type.Object({
  _id: ObjectIdSchema(),
  subject: ObjectIdSchema(), // Required
  chatName: Type.String(), // Required
  chatId: Type.Optional(Type.String()),
  person: ObjectIdSchema(), // Required
  chats: Type.Optional(Type.Array(ChatHistoryItemSchema)),
  ...commonFields.properties
}, { additionalProperties: false })

export type AiChats = Static<typeof aiChatsSchema>
export const aiChatsValidator = getValidator(aiChatsSchema, dataValidator)
export const aiChatsResolver = resolve<AiChats, HookContext>({})
export const aiChatsExternalResolver = resolve<AiChats, HookContext>({})

// Schema for creating new data
export const aiChatsDataSchema = Type.Object({
  ...Type.Omit(aiChatsSchema, ['_id']).properties
}, { additionalProperties: false })

export type AiChatsData = Static<typeof aiChatsDataSchema>
export const aiChatsDataValidator = getValidator(aiChatsDataSchema, dataValidator)
export const aiChatsDataResolver = resolve<AiChatsData, HookContext>({})

// Schema for updating existing data - matching original schema
export const aiChatsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(aiChatsSchema, ['_id'])).properties,

  // MongoDB operators - matching original $push: addToSet(pushPull)
  $push: Type.Optional(Type.Object({
    chats: Type.Optional(Type.Union([
      ChatHistoryItemSchema,
      Type.Object({
        $each: Type.Optional(Type.Array(ChatHistoryItemSchema)),
        $position: Type.Optional(Type.Number())
      }, { additionalProperties: true })
    ]))
  }, { additionalProperties: false })),

  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any()))
}, { additionalProperties: false })

export type AiChatsPatch = Static<typeof aiChatsPatchSchema>
export const aiChatsPatchValidator = getValidator(aiChatsPatchSchema, dataValidator)
export const aiChatsPatchResolver = resolve<AiChatsPatch, HookContext>({})

// Schema for allowed query properties - matching original schema
export const aiChatsQuerySchema = Type.Object({
  ...querySyntax(aiChatsSchema).properties
}, { additionalProperties: false })

export type AiChatsQuery = Static<typeof aiChatsQuerySchema>
export const aiChatsQueryValidator = getValidator(aiChatsQuerySchema, queryValidator)
export const aiChatsQueryResolver = resolve<AiChatsQuery, HookContext>({})
