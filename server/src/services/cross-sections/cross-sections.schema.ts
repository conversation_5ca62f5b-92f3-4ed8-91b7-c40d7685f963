// TypeBox schema for cross-sections service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, addToSet, pull } from '../../utils/common/typebox-schemas.js'

export const crossSectionsSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Required fields from original schema
  hackId: Type.String(), // Required - format: "name:service:id"
  subject: ObjectIdSchema(), // Required

  // CRITICAL Pattern Property - This was completely lost!
  sections: Type.Record(Type.String(), Type.Array(ObjectIdSchema())), // Pattern: ^.*$

  // Common fields
  ...commonFields.properties
}, { additionalProperties: false })

export type CrossSections = Static<typeof crossSectionsSchema>
export const crossSectionsValidator = getValidator(crossSectionsSchema, dataValidator)
export const crossSectionsResolver = resolve<CrossSections, HookContext>({
  // Complex subject resolver from original schema
  subject: async (val, data) => {
    if (!val) return data.hackId.split(':')[2]
    return val
  }
})
export const crossSectionsExternalResolver = resolve<CrossSections, HookContext>({})

export const crossSectionsDataSchema = Type.Object({
  // Required fields for creating new cross-sections
  hackId: Type.String(),
  sections: Type.Record(Type.String(), Type.Array(ObjectIdSchema())),

  // All other fields from main schema (optional for creation)
  ...Type.Partial(Type.Omit(crossSectionsSchema, ['_id', 'hackId', 'sections'])).properties
}, { additionalProperties: false })

export type CrossSectionsData = Static<typeof crossSectionsDataSchema>
export const crossSectionsDataValidator = getValidator(crossSectionsDataSchema, dataValidator)
export const crossSectionsDataResolver = resolve<CrossSectionsData, HookContext>({})

export const crossSectionsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(crossSectionsSchema, ['_id'])).properties,

  // MongoDB operators - matching original $addToSet/$pull for sections
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $addToSet: Type.Optional(addToSet([
    { path: 'sections', type: Type.Array(ObjectIdSchema()) }
  ])),
  $pull: Type.Optional(pull([
    { path: 'sections', type: Type.Array(ObjectIdSchema()) }
  ]))
}, { additionalProperties: false })
export type CrossSectionsPatch = Static<typeof crossSectionsPatchSchema>
export const crossSectionsPatchValidator = getValidator(crossSectionsPatchSchema, dataValidator)
export const crossSectionsPatchResolver = resolve<CrossSectionsPatch, HookContext>({})

// Schema for allowed query properties - matching original schema
export const crossSectionsQuerySchema = Type.Object({
  ...querySyntax(crossSectionsSchema).properties
}, { additionalProperties: false })

export type CrossSectionsQuery = Static<typeof crossSectionsQuerySchema>
export const crossSectionsQueryValidator = getValidator(crossSectionsQuerySchema, queryValidator)
export const crossSectionsQueryResolver = resolve<CrossSectionsQuery, HookContext>({})
