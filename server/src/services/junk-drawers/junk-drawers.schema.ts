// TypeBox schema for junk-drawers service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

export const junkDrawersSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Required fields from original schema
  drawer: Type.String(), // Required
  itemName: Type.String(), // Required
  itemId: Type.String(), // Required

  // Junk drawer fields from original schema
  data: Type.Optional(Type.Any()), // Completely flexible data storage

  // Common fields
  ...commonFields.properties
}, { additionalProperties: false })

export type JunkDrawers = Static<typeof junkDrawersSchema>
export const junkDrawersValidator = getValidator(junkDrawersSchema, dataValidator)
export const junkDrawersResolver = resolve<JunkDrawers, HookContext>({})
export const junkDrawersExternalResolver = resolve<JunkDrawers, HookContext>({})

export const junkDrawersDataSchema = Type.Object({
  // No required fields for creation per original schema (inconsistent with main schema)
  ...Type.Partial(Type.Omit(junkDrawersSchema, ['_id'])).properties
}, { additionalProperties: false })

export type JunkDrawersData = Static<typeof junkDrawersDataSchema>
export const junkDrawersDataValidator = getValidator(junkDrawersDataSchema, dataValidator)
export const junkDrawersDataResolver = resolve<JunkDrawersData, HookContext>({})

export const junkDrawersPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(junkDrawersSchema, ['_id'])).properties,

  // MongoDB operators
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any()))
}, { additionalProperties: true })
export type JunkDrawersPatch = Static<typeof junkDrawersPatchSchema>
export const junkDrawersPatchValidator = getValidator(junkDrawersPatchSchema, dataValidator)
export const junkDrawersPatchResolver = resolve<JunkDrawersPatch, HookContext>({})

// Schema for allowed query properties - matching original schema with custom properties
export const junkDrawersQuerySchema = Type.Object({
  ...querySyntax(junkDrawersSchema).properties,

  // Custom query properties from original schema
  itemName: Type.Optional(Type.Any()),
  data: Type.Optional(Type.Any()),
  pTypes: Type.Optional(Type.Any())
  // Note: commonQueries.properties would be added here in full implementation
}, { additionalProperties: true })

export type JunkDrawersQuery = Static<typeof junkDrawersQuerySchema>
export const junkDrawersQueryValidator = getValidator(junkDrawersQuerySchema, queryValidator)
export const junkDrawersQueryResolver = resolve<JunkDrawersQuery, HookContext>({})
