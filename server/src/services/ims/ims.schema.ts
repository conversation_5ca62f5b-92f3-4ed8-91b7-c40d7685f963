// TypeBox schema for ims service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, addToSet, pull } from '../../utils/common/typebox-schemas.js'

// Simplified participant schema (would need full import in real implementation)
const ParticipantSchema = Type.Object({
  // Simplified participant fields
  fp: Type.Optional(ObjectIdSchema()),
  login: Type.Optional(ObjectIdSchema()),
  name: Type.Optional(Type.String()),
  email: Type.Optional(Type.String())
}, { additionalProperties: true })

// Simplified message schema (would need full import in real implementation)
const MessageSchema = Type.Object({
  // Simplified message fields
  id: Type.Optional(Type.String()),
  body: Type.Optional(Type.String()),
  at: Type.Optional(Type.Any()),
  from: Type.Optional(Type.String())
}, { additionalProperties: true })

export const imsSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Instant messaging fields from original schema
  subject: Type.Optional(ObjectIdSchema()),
  subjectService: Type.Optional(Type.String()),
  plan: Type.Optional(ObjectIdSchema()),
  person: Type.Optional(ObjectIdSchema()),
  team: Type.Optional(ObjectIdSchema()),
  participant: Type.Optional(ParticipantSchema),
  views: Type.Optional(Type.Array(Type.Object({
    id: Type.Optional(Type.String()),
    at: Type.Optional(Type.Any())
  }))),
  orphan: Type.Optional(Type.Boolean()),
  texting: Type.Optional(Type.Boolean()),
  calling: Type.Optional(Type.Boolean()),
  typing: Type.Optional(Type.Array(Type.String())),
  messages: Type.Optional(Type.Array(MessageSchema)),

  // CRITICAL Pattern Property - This was completely lost!
  support: Type.Optional(Type.Record(Type.String(), ParticipantSchema)), // Pattern: ^.*$

  // Common fields
  ...commonFields.properties
}, { additionalProperties: false })

export type Ims = Static<typeof imsSchema>
export const imsValidator = getValidator(imsSchema, dataValidator)
export const imsResolver = resolve<Ims, HookContext>({})
export const imsExternalResolver = resolve<Ims, HookContext>({})

export const imsDataSchema = Type.Object({
  // No required fields for creation per original schema
  ...Type.Partial(Type.Omit(imsSchema, ['_id'])).properties
}, { additionalProperties: false })

export type ImsData = Static<typeof imsDataSchema>
export const imsDataValidator = getValidator(imsDataSchema, dataValidator)
export const imsDataResolver = resolve<ImsData, HookContext>({})

export const imsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(imsSchema, ['_id'])).properties,

  // MongoDB operators - matching original pushPull for messages and typing
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $pull: Type.Optional(pull([
    { path: 'messages', type: MessageSchema },
    { path: 'typing', type: Type.String() }
  ])),
  $addToSet: Type.Optional(addToSet([
    { path: 'messages', type: MessageSchema },
    { path: 'typing', type: Type.String() }
  ])),

  // Custom patch property from original schema for message updates
  'messages.$.body': Type.Optional(Type.String())
}, { additionalProperties: false })
export type ImsPatch = Static<typeof imsPatchSchema>
export const imsPatchValidator = getValidator(imsPatchSchema, dataValidator)
export const imsPatchResolver = resolve<ImsPatch, HookContext>({})

// Schema for allowed query properties - matching original schema with custom properties
export const imsQuerySchema = Type.Object({
  ...querySyntax(imsSchema).properties,

  // Custom query properties from original schema
  'participant.fp': Type.Optional(ObjectIdSchema()),
  'participant.login': Type.Optional(ObjectIdSchema()),
  'participant.name': Type.Optional(Type.Any()),
  'participant.email': Type.Optional(Type.Any()),
  'messages.id': Type.Optional(Type.Any())
}, { additionalProperties: false })

export type ImsQuery = Static<typeof imsQuerySchema>
export const imsQueryValidator = getValidator(imsQuerySchema, queryValidator)
export const imsQueryResolver = resolve<ImsQuery, HookContext>({})
