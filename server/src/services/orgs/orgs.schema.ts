// TypeBox schema for orgs service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, ServiceAddressSchema, PhoneSchema, ImageSchema, TaxSchema } from '../../utils/common/typebox-schemas.js'

// Treasury schema
const TreasurySchema = Type.Object({
  id: Type.Optional(Type.String()),
  balance: Type.Optional(Type.Number()),
  available: Type.Optional(Type.Number()),
  pending: Type.Optional(Type.Number())
}, { additionalProperties: false })

// Main data model schema with all pattern properties restored
export const orgsSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Required fields
  name: Type.String(),

  // Basic org info
  legalName: Type.Optional(Type.String()),
  did: Type.Optional(Type.String()),
  ein: Type.Optional(Type.String()),
  email: Type.Optional(Type.String()),
  emails: Type.Optional(Type.Array(Type.String())),
  phone: Type.Optional(PhoneSchema),
  phones: Type.Optional(Type.Array(PhoneSchema)),
  address: Type.Optional(ServiceAddressSchema),
  addresses: Type.Optional(Type.Array(ServiceAddressSchema)),
  website: Type.Optional(Type.String()),

  // Arrays
  affiliatedOrgs: Type.Optional(Type.Array(ObjectIdSchema())),
  budgets: Type.Optional(Type.Array(ObjectIdSchema())),
  customers: Type.Optional(Type.Array(ObjectIdSchema())),
  hostAccounts: Type.Optional(Type.Array(ObjectIdSchema())),
  industries: Type.Optional(Type.Array(Type.String())),
  images: Type.Optional(Type.Array(ImageSchema)),
  groupIds: Type.Optional(Type.Array(ObjectIdSchema())),
  managementOrgs: Type.Optional(Type.Array(ObjectIdSchema())),
  plans: Type.Optional(Type.Array(ObjectIdSchema())),
  providerAccounts: Type.Optional(Type.Array(ObjectIdSchema())),
  refs: Type.Optional(Type.Array(ObjectIdSchema())),
  threads: Type.Optional(Type.Array(ObjectIdSchema())),
  careAccounts: Type.Optional(Type.Array(ObjectIdSchema())),

  // Profile
  avatar: Type.Optional(ImageSchema),
  coverImage: Type.Optional(ImageSchema),
  public: Type.Optional(Type.Boolean()),
  singleMember: Type.Optional(Type.Boolean()),
  memberCount: Type.Optional(Type.Number()),
  memberCountAt: Type.Optional(Type.Any()),

  // Financial
  ramp_vendor_id: Type.Optional(Type.String()),
  structure: Type.Optional(Type.Union([
    Type.Literal('PARTNERSHIP'),
    Type.Literal('SOLE_PROPRIETOR'),
    Type.Literal('NONPROFIT'),
    Type.Literal('CORPORATION'),
    Type.Literal('S-CORP'),
    Type.Literal('LLC'),
    Type.Literal('LLP'),
    Type.Literal('OTHER')
  ])),
  taxStructure: Type.Optional(Type.String()),

  // Complex objects
  owners: Type.Optional(Type.Array(Type.Object({
    id: ObjectIdSchema(),
    idService: Type.Optional(Type.String()),
    name: Type.Optional(Type.String()),
    email: Type.Optional(Type.String()),
    phone: Type.Optional(PhoneSchema),
    address: Type.Optional(ServiceAddressSchema)
  }))),
  ownerSync: Type.Optional(Type.Boolean()),

  taxes: Type.Optional(Type.Object({
    incomeTaxForm: Type.Optional(Type.String())
  })),

  treasury: Type.Optional(Type.Object({
    customerId: Type.Optional(Type.String()),
    id: Type.Optional(Type.String()),
    business_profile: Type.Optional(Type.Object({
      support_email: Type.Optional(Type.String()),
      annual_revenue: Type.Optional(Type.Object({
        amount: Type.Optional(Type.Number()),
        currency: Type.Optional(Type.String()),
        fiscal_year_end: Type.Optional(Type.String())
      })),
      estimated_worker_count: Type.Optional(Type.Number()),
      mcc: Type.Optional(Type.String()),
      mcc_name: Type.Optional(Type.String()),
      naics: Type.Optional(Type.String()),
      sic: Type.Optional(Type.String()),
      product_description: Type.Optional(Type.String()),
      support_address: Type.Optional(ServiceAddressSchema),
      support_url: Type.Optional(Type.String()),
      url: Type.Optional(Type.String()),
      representatives: Type.Optional(Type.Array(Type.Object({
        isController: Type.Optional(Type.Boolean())
      })))
    }))
  })),

  // CRITICAL Pattern Properties (dynamic keys) - These were completely lost!
  bankAccounts: Type.Optional(Type.Record(Type.String(), Type.Object({
    name: Type.String(),
    id: ObjectIdSchema(),
    default: Type.Boolean()
  }))), // Pattern: ^.*$

  asg: Type.Optional(Type.Record(Type.String(), Type.Object({
    type: Type.Union([Type.Literal('A'), Type.Literal('B')]),
    orgs: Type.Record(Type.String(), Type.Object({
      type: Type.Union([Type.Literal('A'), Type.Literal('B'), Type.Literal('FSO'), Type.Literal('M')])
    }))
  }))), // Pattern: ^.*$

  controls: Type.Optional(Type.Record(Type.String(), Type.Object({
    identical: Type.Number(),
    common: Type.Number(),
    control: Type.Boolean(),
    orgs: Type.Record(Type.String(), Type.Object({
      identical: Type.Number(),
      owners: Type.Array(ObjectIdSchema()),
      parent:Type.Optional(Type.Any()),
      total: Type.Number()
    })),
    brotherSister: Type.Boolean(),
    parentSub: Type.Boolean()
  }))), // Pattern: ^.*$

  groups: Type.Optional(Type.Record(Type.String(), ObjectIdSchema())), // Pattern: ^.*$

  // Missing fields from original schema
  payroll_settings: Type.Optional(Type.Object({
    frequency: Type.Optional(Type.Union([
      Type.Literal('daily'),
      Type.Literal('weekly'),
      Type.Literal('bi-weekly'),
      Type.Literal('semi-monthly'),
      Type.Literal('monthly'),
      Type.Literal('quarterly'),
      Type.Literal('annually'),
      Type.Literal('int')
    ]))
  })),

  // Common fields
  ...commonFields.properties
}, { additionalProperties: true })

export type Orgs = Static<typeof orgsSchema>
export const orgsValidator = getValidator(orgsSchema, dataValidator)
export const orgsResolver = resolve<Orgs, HookContext>({})
export const orgsExternalResolver = resolve<Orgs, HookContext>({})

// Schema for creating new data
export const orgsDataSchema = Type.Object({
  ...Type.Omit(orgsSchema, ['_id']).properties
}, { additionalProperties: false })

export type OrgsData = Static<typeof orgsDataSchema>
export const orgsDataValidator = getValidator(orgsDataSchema, dataValidator)
export const orgsDataResolver = resolve<OrgsData, HookContext>({})

// Schema for updating existing data - matching original schema
export const orgsPatchSchema = Type.Object({
  // All schema properties (for direct field updates)
  ...Type.Omit(orgsSchema, ['_id']).properties,

  // Partial schema properties (for optional updates)
  ...Type.Partial(Type.Omit(orgsSchema, ['_id'])).properties,

  // Common MongoDB operators
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any()))
}, { additionalProperties: true })

export type OrgsPatch = Static<typeof orgsPatchSchema>
export const orgsPatchValidator = getValidator(orgsPatchSchema, dataValidator)
export const orgsPatchResolver = resolve<OrgsPatch, HookContext>({})

// Schema for allowed query properties - matching original schema with custom query fields
export const orgsQuerySchema = Type.Object({
  ...querySyntax(Type.Object({
    ...orgsSchema.properties,
    // Custom query properties from original schema
    name:Type.Optional(Type.Any()),
    email:Type.Optional(Type.Any()),
    phone:Type.Optional(Type.Any()),
    _owners:Type.Optional(Type.Any()),
    _attribute:Type.Optional(Type.Any())
  })).properties,

  // Complex query operators for specific fields
  'owners.id': Type.Optional(ObjectIdSchema()),

  owners: Type.Optional(Type.Union([
    Type.Array(Type.Object({
      id: ObjectIdSchema(),
      idService: Type.Optional(Type.String()),
      name: Type.Optional(Type.String()),
      email: Type.Optional(Type.String()),
      phone: Type.Optional(PhoneSchema),
      address: Type.Optional(ServiceAddressSchema)
    })),
    Type.Object({
      $elemMatch: Type.Optional(Type.Record(Type.String(), Type.Any()))
    })
  ]))
}, { additionalProperties: true })

export type OrgsQuery = Static<typeof orgsQuerySchema>
export const orgsQueryValidator = getValidator(orgsQuerySchema, queryValidator)
export const orgsQueryResolver = resolve<OrgsQuery, HookContext>({})
