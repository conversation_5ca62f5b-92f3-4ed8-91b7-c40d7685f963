// TypeBox schema for hosts service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, ImageSchema, VideoSchema, ServiceAddressSchema, PhoneSchema, addToSet, pull } from '../../utils/common/typebox-schemas.js'

// Guide enum for roles
const guideEnum = ['care_director', 'plan_guide', 'compliance', 'finance', 'physician'] as const

export const hostsSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Required fields from original schema
  org: ObjectIdSchema(), // Required
  dba: Type.String(), // Required

  // Host configuration fields from original schema
  appDefault: Type.Optional(Type.Boolean()),
  description: Type.Optional(Type.String()),
  avatar: Type.Optional(ImageSchema),
  subdomain: Type.Optional(Type.String()),
  allVideos: Type.Optional(Type.Array(ObjectIdSchema())), // uploadIds
  phones: Type.Optional(Type.Array(PhoneSchema)),
  emails: Type.Optional(Type.Array(Type.String())),
  locations: Type.Optional(Type.Array(ServiceAddressSchema)),
  refs: Type.Optional(Type.Array(ObjectIdSchema())),
  teams: Type.Optional(Type.Array(ObjectIdSchema())),
  npn: Type.Optional(Type.String()),
  publicSupport: Type.Optional(ObjectIdSchema()), // team
  roles: Type.Optional(Type.Array(Type.Union([
    Type.Literal('care_director'), Type.Literal('plan_guide'),
    Type.Literal('compliance'), Type.Literal('finance'), Type.Literal('physician')
  ]))),
  broker: Type.Optional(Type.Object({
    active: Type.Optional(Type.Boolean()),
    ichra: Type.Optional(Type.Boolean())
  })),

  // CRITICAL Pattern Properties - All 4 were completely lost!
  shopStatuses: Type.Optional(Type.Record(Type.String(), Type.Object({
    label: Type.Optional(Type.String()),
    color: Type.Optional(Type.String())
  }))), // Pattern: ^.*$

  plans: Type.Optional(Type.Record(Type.String(), Type.Object({
    team: Type.Optional(ObjectIdSchema()),
    payContract: Type.Optional(ObjectIdSchema())
  }))), // Pattern: ^.*$

  videos: Type.Optional(Type.Object({
    intro: Type.Optional(Type.Record(Type.String(), VideoSchema)) // Pattern: ^.*$
  })),

  states: Type.Optional(Type.Record(Type.String({ pattern: '[A-Z]{2}' }), Type.Object({
    state: Type.Optional(Type.String()), // AK, UT
    counties: Type.Optional(Type.Record(Type.String(), Type.Object({
      all: Type.Optional(Type.Boolean()),
      cities: Type.Optional(Type.Array(Type.String()))
    }))), // Nested pattern: ^.*$
    all: Type.Optional(Type.Boolean())
  }))), // Pattern: [A-Z]{2}

  // Common fields
  ...commonFields.properties
}, { additionalProperties: false })

export type Hosts = Static<typeof hostsSchema>
export const hostsValidator = getValidator(hostsSchema, dataValidator)
export const hostsResolver = resolve<Hosts, HookContext>({
  // Subdomain resolver from original schema
  subdomain: async (val) => {
    if (val) {
      if (['admin', 'console', 'host', 'app'].includes(val)) {
        throw new Error('Forbidden subdomain')
      }
      return val.trim().toLowerCase()
    }
    return val
  }
})
export const hostsExternalResolver = resolve<Hosts, HookContext>({})

export const hostsDataSchema = Type.Object({
  // Required fields for creating new hosts
  org: ObjectIdSchema(),
  dba: Type.String(),

  // All other fields from main schema (optional for creation)
  ...Type.Partial(Type.Omit(hostsSchema, ['_id', 'org', 'dba'])).properties
}, { additionalProperties: false })

export type HostsData = Static<typeof hostsDataSchema>
export const hostsDataValidator = getValidator(hostsDataSchema, dataValidator)
export const hostsDataResolver = resolve<HostsData, HookContext>({})

export const hostsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(hostsSchema, ['_id'])).properties,

  // MongoDB operators
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $addToSet: Type.Optional(addToSet([
    { path: 'allVideos', type: ObjectIdSchema() },
    { path: 'phones', type: PhoneSchema },
    { path: 'emails', type: Type.String() },
    { path: 'locations', type: ServiceAddressSchema },
    { path: 'refs', type: ObjectIdSchema() },
    { path: 'teams', type: ObjectIdSchema() },
    { path: 'roles', type: Type.String() }
  ])),
  $pull: Type.Optional(pull([
    { path: 'allVideos', type: ObjectIdSchema() },
    { path: 'phones', type: PhoneSchema },
    { path: 'emails', type: Type.String() },
    { path: 'locations', type: ServiceAddressSchema },
    { path: 'refs', type: ObjectIdSchema() },
    { path: 'teams', type: ObjectIdSchema() },
    { path: 'roles', type: Type.String() }
  ]))
}, { additionalProperties: false })
export type HostsPatch = Static<typeof hostsPatchSchema>
export const hostsPatchValidator = getValidator(hostsPatchSchema, dataValidator)
export const hostsPatchResolver = resolve<HostsPatch, HookContext>({})

// Schema for allowed query properties - matching original schema
export const hostsQuerySchema = Type.Object({
  ...querySyntax(hostsSchema).properties
}, { additionalProperties: false })

export type HostsQuery = Static<typeof hostsQuerySchema>
export const hostsQueryValidator = getValidator(hostsQuerySchema, queryValidator)
export const hostsQueryResolver = resolve<HostsQuery, HookContext>({})
