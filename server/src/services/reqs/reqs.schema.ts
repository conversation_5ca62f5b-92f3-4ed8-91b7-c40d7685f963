// TypeBox schema for reqs service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

export const reqsSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Minimal schema matching original - specific request fields with flexible data
  fingerprint: Type.Optional(Type.String()),
  refName: Type.Optional(Type.String()),

  // Common fields
  ...commonFields.properties
}, { additionalProperties: true })

export type Reqs = Static<typeof reqsSchema>
export const reqsValidator = getValidator(reqsSchema, dataValidator)
export const reqsResolver = resolve<Reqs, HookContext>({})
export const reqsExternalResolver = resolve<Reqs, HookContext>({})

export const reqsDataSchema = Type.Object({
  // No required fields for creation per original schema
  ...Type.Partial(Type.Omit(reqsSchema, ['_id'])).properties
}, { additionalProperties: true })

export type ReqsData = Static<typeof reqsDataSchema>
export const reqsDataValidator = getValidator(reqsDataSchema, dataValidator)
export const reqsDataResolver = resolve<ReqsData, HookContext>({})

export const reqsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(reqsSchema, ['_id'])).properties,

  // MongoDB operators
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any()))
}, { additionalProperties: true })
export type ReqsPatch = Static<typeof reqsPatchSchema>
export const reqsPatchValidator = getValidator(reqsPatchSchema, dataValidator)
export const reqsPatchResolver = resolve<ReqsPatch, HookContext>({})

// Schema for allowed query properties - matching original schema
export const reqsQuerySchema = Type.Object({
  ...querySyntax(reqsSchema).properties
}, { additionalProperties: true })

export type ReqsQuery = Static<typeof reqsQuerySchema>
export const reqsQueryValidator = getValidator(reqsQuerySchema, queryValidator)
export const reqsQueryResolver = resolve<ReqsQuery, HookContext>({})
