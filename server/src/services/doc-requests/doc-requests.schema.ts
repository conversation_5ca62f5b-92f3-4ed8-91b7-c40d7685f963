// TypeBox schema for doc-requests service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, ImageSchema, PhoneSchema } from '../../utils/common/typebox-schemas.js'

// Employee schema for doc requests
const EeSchema = Type.Object({
  name: Type.Optional(Type.String()),
  email: Type.Optional(Type.String()),
  phone: Type.Optional(PhoneSchema),
  position: Type.Optional(Type.String()),
  department: Type.Optional(Type.String())
}, { additionalProperties: false })

export const docRequestsSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Document request configuration
  types: Type.Optional(Type.Array(Type.String())),
  orgName: Type.Optional(Type.String()),
  orgAvatar: Type.Optional(Type.String()),
  org: Type.Optional(ObjectIdSchema()),
  plan: Type.Optional(ObjectIdSchema()),
  fingerprint: Type.Optional(Type.String()),
  refName: Type.Optional(Type.String()),
  doc: Type.Optional(Type.String()),
  eeCount: Type.Optional(Type.Number()),
  fteCount: Type.Optional(Type.Number()),
  email: Type.Optional(Type.String()),
  phone: Type.Optional(PhoneSchema),
  name: Type.Optional(Type.String()),
  optIn: Type.Optional(Type.Boolean()),
  status: Type.Optional(Type.Union([Type.Literal('complete'), Type.Literal('partial')])),
  states: Type.Optional(Type.Array(Type.String())),
  docType: Type.Optional(Type.Union([Type.Literal('smb')])),
  employees: Type.Optional(Type.Array(EeSchema)),

  // CRITICAL Pattern Property - This was completely lost!
  files: Type.Optional(Type.Record(Type.String(), ImageSchema)), // Pattern: ^.*$

  // Common fields
  ...commonFields.properties
}, { additionalProperties: true })

export type DocRequests = Static<typeof docRequestsSchema>
export const docRequestsValidator = getValidator(docRequestsSchema, dataValidator)
export const docRequestsResolver = resolve<DocRequests, HookContext>({})
export const docRequestsExternalResolver = resolve<DocRequests, HookContext>({})

export const docRequestsDataSchema = Type.Object({
  ...Type.Partial(Type.Omit(docRequestsSchema, ['_id'])).properties
}, { additionalProperties: true })

export type DocRequestsData = Static<typeof docRequestsDataSchema>
export const docRequestsDataValidator = getValidator(docRequestsDataSchema, dataValidator)
export const docRequestsDataResolver = resolve<DocRequestsData, HookContext>({})

export const docRequestsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(docRequestsSchema, ['_id'])).properties,

  // MongoDB operators
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any()))
}, { additionalProperties: true })
export type DocRequestsPatch = Static<typeof docRequestsPatchSchema>
export const docRequestsPatchValidator = getValidator(docRequestsPatchSchema, dataValidator)
export const docRequestsPatchResolver = resolve<DocRequestsPatch, HookContext>({})

// Schema for allowed query properties - matching original schema
export const docRequestsQuerySchema = Type.Object({
  ...querySyntax(docRequestsSchema).properties
}, { additionalProperties: true })

export type DocRequestsQuery = Static<typeof docRequestsQuerySchema>
export const docRequestsQueryValidator = getValidator(docRequestsQuerySchema, queryValidator)
export const docRequestsQueryResolver = resolve<DocRequestsQuery, HookContext>({})

// Export for backward compatibility with business logic
export const eeFactsSchema = Type.Object({
  name: Type.Optional(Type.String()),
  value: Type.Optional(Type.Any()),
  type: Type.Optional(Type.String())
}, { additionalProperties: false })
