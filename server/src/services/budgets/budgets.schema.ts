// TypeBox schema for budgets service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, addToSet, pull } from '../../utils/common/typebox-schemas.js'

// LastSync schema for budget sync history
const LastSyncSchema = Type.Object({
  at: Type.Optional(Type.Any()),
  by: Type.Optional(ObjectIdSchema()),
  status: Type.Optional(Type.String())
}, { additionalProperties: false })

// PriorAuth schema for budget authorization
const PriorAuthSchema = Type.Object({
  required: Type.Optional(Type.Boolean()),
  approvers: Type.Optional(Type.Array(ObjectIdSchema()))
}, { additionalProperties: false })

// PriorMonths schema for historical budget data
const PriorMonthsSchema = Type.Object({
  amount: Type.Optional(Type.Number()),
  recurs: Type.Optional(Type.Number()),
  parent: Type.Optional(ObjectIdSchema()),
  careAccount: Type.Optional(ObjectIdSchema()),
  spent_sub: Type.Optional(Type.Number()),
  spent_pending: Type.Optional(Type.Number()),
  spent_pending_sub: Type.Optional(Type.Number()),
  spent: Type.Optional(Type.Number())
}, { additionalProperties: false })

export const budgetsSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Required fields from original schema
  owner: ObjectIdSchema(), // Required
  name: Type.String(), // Required
  moov_id: Type.String(), // Required

  // Budget amounts and calculations
  amount: Type.Optional(Type.Number()),
  assigned_amount: Type.Optional(Type.Number()),
  assigned_recurs: Type.Optional(Type.Number()),
  recurs: Type.Optional(Type.Number()),
  spent: Type.Optional(Type.Number()),
  spent_pending: Type.Optional(Type.Number()),
  spent_pending_sub: Type.Optional(Type.Number()),
  spent_sub: Type.Optional(Type.Number()),

  // Arrays of related entities
  approvers: Type.Optional(Type.Array(ObjectIdSchema())),
  expenses: Type.Optional(Type.Array(ObjectIdSchema())),
  children: Type.Optional(Type.Array(ObjectIdSchema())),
  managers: Type.Optional(Type.Array(ObjectIdSchema())),
  members: Type.Optional(Type.Array(ObjectIdSchema())),
  ramp_whitelist: Type.Optional(Type.Array(Type.String())),
  mcc_whitelist: Type.Optional(Type.Array(Type.String())),
  mcc_blacklist: Type.Optional(Type.Array(Type.String())),
  syncHistory: Type.Optional(Type.Array(LastSyncSchema)),

  // Related entities
  careAccount: Type.Optional(ObjectIdSchema()),
  parent: Type.Optional(ObjectIdSchema()),

  // Configuration
  category: Type.Optional(Type.String()),
  connect_id: Type.Optional(Type.String()),
  lastInc: Type.Optional(Type.String()),
  lastSync: Type.Optional(LastSyncSchema),
  runSync: Type.Optional(Type.String()),
  rampSpendProgram: Type.Optional(Type.String()),
  priorAuth: Type.Optional(PriorAuthSchema),

  // Historical data
  priorMonths: Type.Optional(Type.Array(PriorMonthsSchema)),

  // Common fields
  ...commonFields.properties
}, { additionalProperties: false })

export type Budgets = Static<typeof budgetsSchema>
export const budgetsValidator = getValidator(budgetsSchema, dataValidator)
export const budgetsResolver = resolve<Budgets, HookContext>({})
export const budgetsExternalResolver = resolve<Budgets, HookContext>({})

export const budgetsDataSchema = Type.Object({
  // Required fields for creating new budgets (excluding amount, recurs, assigned_amount, assigned_recurs per original)
  owner: ObjectIdSchema(),
  name: Type.String(),
  moov_id: Type.String(),

  // All other fields from main schema (optional for creation)
  ...Type.Partial(Type.Omit(budgetsSchema, ['_id', 'owner', 'name', 'moov_id', 'amount', 'recurs', 'assigned_amount', 'assigned_recurs'])).properties
}, { additionalProperties: false })

export type BudgetsData = Static<typeof budgetsDataSchema>
export const budgetsDataValidator = getValidator(budgetsDataSchema, dataValidator)
export const budgetsDataResolver = resolve<BudgetsData, HookContext>({})

export const budgetsPatchSchema = Type.Object({
  // All schema properties except amount fields (per original createPatchSchema)
  ...Type.Omit(budgetsSchema, ['_id', 'amount', 'recurs', 'assigned_amount', 'assigned_recurs']).properties,

  // Partial schema properties (for optional updates)
  ...Type.Partial(Type.Omit(budgetsSchema, ['_id'])).properties,

  // MongoDB operators - matching original $inc, $pull, $addToSet with listArgs
  $inc: Type.Optional(Type.Record(Type.String(), Type.Number())),
  $pull: Type.Optional(pull([
    { path: 'syncHistory', type: LastSyncSchema },
    { path: 'mcc_whitelist', type: Type.String() },
    { path: 'mcc_blacklist', type: Type.String() },
    { path: 'budgets', type: Type.Array(ObjectIdSchema()) },
    { path: 'cards', type: ObjectIdSchema() }
  ])),
  $addToSet: Type.Optional(addToSet([
    { path: 'syncHistory', type: LastSyncSchema },
    { path: 'mcc_whitelist', type: Type.String() },
    { path: 'mcc_blacklist', type: Type.String() },
    { path: 'budgets', type: Type.Array(ObjectIdSchema()) },
    { path: 'cards', type: ObjectIdSchema() }
  ])),

  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any()))
}, { additionalProperties: false })
export type BudgetsPatch = Static<typeof budgetsPatchSchema>
export const budgetsPatchValidator = getValidator(budgetsPatchSchema, dataValidator)
export const budgetsPatchResolver = resolve<BudgetsPatch, HookContext>({})

// Schema for allowed query properties - matching original schema with custom fields
export const budgetsQuerySchema = Type.Object({
  ...querySyntax(budgetsSchema).properties,

  // Custom query properties from original schema
  name: Type.Optional(Type.Any()),

  // Exists query for parent field
  parent: Type.Optional(Type.Union([
    ObjectIdSchema(),
    Type.Object({
      $exists: Type.Boolean()
    })
  ]))
}, { additionalProperties: false })

export type BudgetsQuery = Static<typeof budgetsQuerySchema>
export const budgetsQueryValidator = getValidator(budgetsQuerySchema, queryValidator)
export const budgetsQueryResolver = resolve<BudgetsQuery, HookContext>({})
