// TypeBox schema for cats service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, ImageSchema, addToSet, pull } from '../../utils/common/typebox-schemas.js'

export const catsSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Required fields from original schema
  name: Type.String(), // Required

  // Category configuration
  avatar: Type.Optional(ImageSchema),
  org: Type.Optional(ObjectIdSchema()),
  description: Type.Optional(Type.String()),
  code_regex: Type.Optional(Type.String()),

  // Arrays of related entities
  managers: Type.Optional(Type.Array(ObjectIdSchema())),
  writers: Type.Optional(Type.Array(ObjectIdSchema())),
  images: Type.Optional(Type.Array(ImageSchema)),
  conditions: Type.Optional(Type.Array(ObjectIdSchema())),
  procedures: Type.Optional(Type.Array(ObjectIdSchema())),
  meds: Type.Optional(Type.Array(ObjectIdSchema())),

  // Common fields
  ...commonFields.properties
}, { additionalProperties: true })

export type Cats = Static<typeof catsSchema>
export const catsValidator = getValidator(catsSchema, dataValidator)
export const catsResolver = resolve<Cats, HookContext>({})
export const catsExternalResolver = resolve<Cats, HookContext>({})

export const catsDataSchema = Type.Object({
  // Required fields for creating new cats
  name: Type.String(),

  // All other fields from main schema (optional for creation)
  ...Type.Partial(Type.Omit(catsSchema, ['_id', 'name'])).properties
}, { additionalProperties: true })

export type CatsData = Static<typeof catsDataSchema>
export const catsDataValidator = getValidator(catsDataSchema, dataValidator)
export const catsDataResolver = resolve<CatsData, HookContext>({})

export const catsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(catsSchema, ['_id'])).properties,

  // MongoDB operators - matching original pushPull configuration
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $addToSet: Type.Optional(addToSet([
    { path: 'procedures', type: ObjectIdSchema() },
    { path: 'meds', type: ObjectIdSchema() },
    { path: 'conditions', type: ObjectIdSchema() },
    { path: 'managers', type: ObjectIdSchema() },
    { path: 'writers', type: ObjectIdSchema() }
  ])),
  $pull: Type.Optional(pull([
    { path: 'procedures', type: ObjectIdSchema() },
    { path: 'meds', type: ObjectIdSchema() },
    { path: 'conditions', type: ObjectIdSchema() },
    { path: 'managers', type: ObjectIdSchema() },
    { path: 'writers', type: ObjectIdSchema() }
  ]))
}, { additionalProperties: true })
export type CatsPatch = Static<typeof catsPatchSchema>
export const catsPatchValidator = getValidator(catsPatchSchema, dataValidator)
export const catsPatchResolver = resolve<CatsPatch, HookContext>({})

// Schema for allowed query properties - matching original schema
export const catsQuerySchema = Type.Object({
  ...querySyntax(catsSchema).properties,

  // Custom query properties from original schema
  name: Type.Optional(Type.Any())
}, { additionalProperties: true })

export type CatsQuery = Static<typeof catsQuerySchema>
export const catsQueryValidator = getValidator(catsQuerySchema, queryValidator)
export const catsQueryResolver = resolve<CatsQuery, HookContext>({})
