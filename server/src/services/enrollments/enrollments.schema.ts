// TypeBox schema for enrollments service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, ServiceAddressSchema, addToSet } from '../../utils/common/typebox-schemas.js'

// Relationships enum from original schema
const relationships = ['self', 'spouse', 'child', 'parent', 'sibling', 'other'] as const

// County schema for enrollment location
const CountySchema = Type.Object({
  fips: Type.Optional(Type.String()),
  name: Type.Optional(Type.String()),
  stateCode: Type.Optional(Type.String())
}, { additionalProperties: false })

// Cafe enrollment schema for benefits
const CafeEnrollSchema = Type.Object({
  amount: Type.Optional(Type.Number()),
  enrolled: Type.Optional(Type.Boolean()),
  contribution: Type.Optional(Type.Number())
}, { additionalProperties: false })

// Paids schema for claim payments
const PaidsSchema = Type.Object({
  paid: Type.Optional(Type.Number()),
  paidAt: Type.Optional(Type.Any()),
  paidBy: Type.Optional(ObjectIdSchema()),
  paidMethod: Type.Optional(Type.String())
}, { additionalProperties: false })

// Main data model schema
export const enrollmentsSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Required fields from original schema
  org: ObjectIdSchema(), // Required
  group: ObjectIdSchema(), // Required
  person: ObjectIdSchema(), // Required
  plan: ObjectIdSchema(), // Required
  version: Type.String(), // Required
  close: Type.Any(), // Required
  idempotency_key: Type.String(), // Required

  // Enrollment configuration
  name: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  planYear: Type.Optional(Type.String()),
  address: Type.Optional(Type.Object({
    postal: Type.String(),
    region: Type.String(),
    ...ServiceAddressSchema.properties
  })),
  county: Type.Optional(CountySchema),
  spec: Type.Optional(ObjectIdSchema()),
  status: Type.Optional(Type.Union([
    Type.Literal('not_started'), Type.Literal('open'), Type.Literal('review'),
    Type.Literal('complete'), Type.Literal('closed')
  ])),
  statusNote: Type.Optional(Type.String()),
  optOut: Type.Optional(Type.Any()),
  open: Type.Optional(Type.String()),
  terminated: Type.Optional(Type.Boolean()),
  terminatedAt: Type.Optional(Type.Any()),
  terminatedBy: Type.Optional(ObjectIdSchema()),
  enrolledAt: Type.Optional(Type.Any()),
  ichra: Type.Optional(Type.Boolean()),
  shop: Type.Optional(ObjectIdSchema()),
  type: Type.Optional(Type.Union([Type.Literal('single'), Type.Literal('family')])),
  householdIncome: Type.Optional(Type.Number()),

  // CRITICAL Pattern Properties - All were completely lost!
  enrolled: Type.Optional(Type.Record(Type.String(), Type.Object({
    age: Type.Optional(Type.Number()),
    dob: Type.Optional(Type.String()),
    ssn: Type.Optional(Type.String()),
    firstName: Type.Optional(Type.String()),
    lastName: Type.Optional(Type.String()),
    gender: Type.Optional(Type.String()),
    relation: Type.Optional(Type.Union([
      Type.Literal('self'), Type.Literal('spouse'), Type.Literal('child'),
      Type.Literal('parent'), Type.Literal('sibling'), Type.Literal('other')
    ])),
    zip: Type.Optional(Type.String()),
    point: Type.Optional(Type.Array(Type.Number())),
    monthsSinceSmoked: Type.Optional(Type.Number()),
    dependent: Type.Optional(Type.Boolean()),
    disabled: Type.Optional(Type.Boolean()),
    annualIncome: Type.Optional(Type.Number()),
    incarcerated: Type.Optional(Type.Boolean())
  }))), // Pattern: ^.*$

  cafe: Type.Optional(Type.Object({
    hsa: Type.Optional(CafeEnrollSchema),
    fsa: Type.Optional(CafeEnrollSchema),
    dcp: Type.Optional(CafeEnrollSchema),
    pop: Type.Optional(CafeEnrollSchema),
    def: Type.Optional(CafeEnrollSchema),
    cash: Type.Optional(CafeEnrollSchema)
  })),

  lastClaimCoverage: Type.Optional(ObjectIdSchema()),
  claimPayments: Type.Optional(Type.Array(ObjectIdSchema())),

  // NESTED Pattern Properties - This was completely lost!
  patientClaims: Type.Optional(Type.Record(Type.String(), Type.Record(Type.String(), PaidsSchema))), // Pattern: ^.*$ -> Pattern: ^.*$

  // Common fields
  ...commonFields.properties
}, { additionalProperties: false })

export type Enrollments = Static<typeof enrollmentsSchema>
export const enrollmentsValidator = getValidator(enrollmentsSchema, dataValidator)
export const enrollmentsResolver = resolve<Enrollments, HookContext>({})
export const enrollmentsExternalResolver = resolve<Enrollments, HookContext>({})

// Schema for creating new data
export const enrollmentsDataSchema = Type.Object({
  // No required fields for creation per original schema
  ...Type.Partial(Type.Omit(enrollmentsSchema, ['_id'])).properties
}, { additionalProperties: false })

export type EnrollmentsData = Static<typeof enrollmentsDataSchema>
export const enrollmentsDataValidator = getValidator(enrollmentsDataSchema, dataValidator)
export const enrollmentsDataResolver = resolve<EnrollmentsData, HookContext>({})

// Schema for updating existing data
export const enrollmentsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(enrollmentsSchema, ['_id'])).properties,

  // MongoDB operators
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $inc: Type.Optional(Type.Record(Type.String(), Type.Number())),
  $addToSet: Type.Optional(addToSet([
    { path: 'claimPayments', type: ObjectIdSchema() }
  ]))
}, { additionalProperties: false })

export type EnrollmentsPatch = Static<typeof enrollmentsPatchSchema>
export const enrollmentsPatchValidator = getValidator(enrollmentsPatchSchema, dataValidator)
export const enrollmentsPatchResolver = resolve<EnrollmentsPatch, HookContext>({})

// Schema for allowed query properties - matching original schema
export const enrollmentsQuerySchema = Type.Object({
  ...querySyntax(enrollmentsSchema).properties
}, { additionalProperties: false })

export type EnrollmentsQuery = Static<typeof enrollmentsQuerySchema>
export const enrollmentsQueryValidator = getValidator(enrollmentsQuerySchema, queryValidator)
export const enrollmentsQueryResolver = resolve<EnrollmentsQuery, HookContext>({})
