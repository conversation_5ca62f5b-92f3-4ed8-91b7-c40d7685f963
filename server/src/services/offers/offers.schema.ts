// TypeBox schema for offers service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

// Guide enum for roles
const guideEnum = ['care_director', 'plan_guide', 'compliance', 'finance', 'physician'] as const

// Fee enum for fee types
const feeEnum = ['flat', 'percentage', 'per_member'] as const

export const offersSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Required fields from original schema
  plan: ObjectIdSchema(), // Required
  host: ObjectIdSchema(), // Required
  fee: Type.Number(), // Required
  feeType: Type.Union([
    Type.Literal('flat'), Type.Literal('percentage'), Type.Literal('per_member')
  ]), // Required

  // Commission offer fields from original schema
  contract: Type.Optional(ObjectIdSchema()),
  role: Type.Optional(Type.Union([
    Type.Literal('care_director'), Type.Literal('plan_guide'),
    Type.Literal('compliance'), Type.Literal('finance'), Type.Literal('physician')
  ])),
  threads: Type.Optional(Type.Array(ObjectIdSchema())),
  status: Type.Optional(Type.Union([
    Type.Literal('pending'), Type.Literal('rejected'), Type.Literal('active')
  ])),

  // Common fields
  ...commonFields.properties
}, { additionalProperties: false })

export type Offers = Static<typeof offersSchema>
export const offersValidator = getValidator(offersSchema, dataValidator)
export const offersResolver = resolve<Offers, HookContext>({
  // Status resolver from original schema
  status: async (val) => {
    if (!val) return 'pending'
    return val
  }
})
export const offersExternalResolver = resolve<Offers, HookContext>({})

export const offersDataSchema = Type.Object({
  // Required fields for creating new offers
  plan: ObjectIdSchema(),
  host: ObjectIdSchema(),
  fee: Type.Number(),
  feeType: Type.Union([
    Type.Literal('flat'), Type.Literal('percentage'), Type.Literal('per_member')
  ]),

  // All other fields from main schema (optional for creation)
  ...Type.Partial(Type.Omit(offersSchema, ['_id', 'plan', 'host', 'fee', 'feeType'])).properties
}, { additionalProperties: false })

export type OffersData = Static<typeof offersDataSchema>
export const offersDataValidator = getValidator(offersDataSchema, dataValidator)
export const offersDataResolver = resolve<OffersData, HookContext>({})

export const offersPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(offersSchema, ['_id'])).properties,

  // MongoDB operators
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any()))
}, { additionalProperties: false })
export type OffersPatch = Static<typeof offersPatchSchema>
export const offersPatchValidator = getValidator(offersPatchSchema, dataValidator)
export const offersPatchResolver = resolve<OffersPatch, HookContext>({})

// Schema for allowed query properties - matching original schema
export const offersQuerySchema = Type.Object({
  ...querySyntax(offersSchema).properties
}, { additionalProperties: false })

export type OffersQuery = Static<typeof offersQuerySchema>
export const offersQueryValidator = getValidator(offersQuerySchema, queryValidator)
export const offersQueryResolver = resolve<OffersQuery, HookContext>({})
