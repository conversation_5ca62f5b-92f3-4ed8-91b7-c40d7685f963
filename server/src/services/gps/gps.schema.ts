// TypeBox schema for gps service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, ImageSchema, addToSet } from '../../utils/common/typebox-schemas.js'

// Simplified schemas (would need full imports in real implementation)
const EmployerContributionSchema = Type.Object({
  // Simplified employer contribution fields
  amount: Type.Optional(Type.Number()),
  percentage: Type.Optional(Type.Number())
}, { additionalProperties: true })

const SimStatsSchema = Type.Object({
  // Simplified sim stats fields
  spend: Type.Optional(Type.Number()),
  count: Type.Optional(Type.Number()),
  premium: Type.Optional(Type.Number())
}, { additionalProperties: true })

const CurrentStatsSchema = Type.Object({
  spend: Type.Optional(Type.Number()),
  spendCount: Type.Optional(Type.Number()),
  spendPremium: Type.Optional(Type.Number()),
  count: Type.Optional(Type.Number()),
  premium: Type.Optional(Type.Number()),
  premiumByKey: Type.Optional(Type.Record(Type.String(), Type.Number())),
  countByKey: Type.Optional(Type.Record(Type.String(), Type.Number()))
}, { additionalProperties: false })

const EmployeeSchema = Type.Object({
  shop: Type.Optional(ObjectIdSchema()),
  coverage: Type.Optional(Type.String()),
  sim: Type.Optional(ObjectIdSchema()),
  simError: Type.Optional(Type.String()),
  bestPlan: Type.Optional(Type.String()),
  bestPlanPtc: Type.Optional(Type.String()),
  bestAlt: Type.Optional(Type.String())
  // Plus eeSchema.properties would be spread here
}, { additionalProperties: true })

export const gpsSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Group plan simulation fields from original schema
  org: Type.Optional(ObjectIdSchema()),
  plan: Type.Optional(ObjectIdSchema()),
  runRequest: Type.Optional(Type.Any()),
  companyName: Type.Optional(Type.String()),
  companyAvatar: Type.Optional(Type.String()),
  email: Type.Optional(Type.String()),
  name: Type.Optional(Type.String()),
  eeCount: Type.Optional(Type.Number()),
  planName: Type.Optional(Type.String()),
  employerContribution: Type.Optional(EmployerContributionSchema),
  ale: Type.Optional(Type.Boolean()),
  owner: Type.Optional(ObjectIdSchema()), // person
  editors: Type.Optional(Type.Array(ObjectIdSchema())),
  vectorId: Type.Optional(Type.String()),
  lastSim: Type.Optional(Type.Any()),
  groupCompare: Type.Optional(Type.Boolean()),
  simProgress: Type.Optional(Type.Number()),
  simStats: Type.Optional(SimStatsSchema),
  currentStats: Type.Optional(CurrentStatsSchema),
  employees: Type.Optional(Type.Array(EmployeeSchema)),

  // CRITICAL Pattern Properties - All 3 were completely lost!
  coverages: Type.Optional(Type.Record(Type.String(), Type.Object({
    id: Type.Optional(Type.String()),
    compare_id: Type.Optional(Type.String()),
    similar: Type.Optional(Type.Record(Type.String(), Type.String())), // Nested pattern: ^.*$
    mostSimilar: Type.Optional(Type.String()),
    knownKeys: Type.Optional(Type.Array(Type.String())),
    files: Type.Optional(Type.Array(ImageSchema)),
    fromFile: Type.Optional(Type.Boolean())
    // Plus coverageCalcSchema.properties would be spread here
  }, { additionalProperties: true }))), // Pattern: ^.*$

  employerContributionReports: Type.Optional(Type.Record(Type.String(), Type.Object({
    person: Type.Optional(ObjectIdSchema()),
    gps: Type.Optional(ObjectIdSchema()),
    updatedAt: Type.Optional(Type.Any()),
    data: Type.Optional(EmployerContributionSchema)
  }))), // Pattern: ^.*$

  // Common fields
  ...commonFields.properties
}, { additionalProperties: false })

export type Gps = Static<typeof gpsSchema>
export const gpsValidator = getValidator(gpsSchema, dataValidator)
export const gpsResolver = resolve<Gps, HookContext>({})
export const gpsExternalResolver = resolve<Gps, HookContext>({})

export const gpsDataSchema = Type.Object({
  // No required fields for creation per original schema
  ...Type.Partial(Type.Omit(gpsSchema, ['_id'])).properties
}, { additionalProperties: false })

export type GpsData = Static<typeof gpsDataSchema>
export const gpsDataValidator = getValidator(gpsDataSchema, dataValidator)
export const gpsDataResolver = resolve<GpsData, HookContext>({})

export const gpsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(gpsSchema, ['_id'])).properties,

  // MongoDB operators
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $addToSet: Type.Optional(addToSet([
    { path: 'editors', type: ObjectIdSchema() },
    { path: 'employees', type: EmployeeSchema }
  ]))
}, { additionalProperties: false })
export type GpsPatch = Static<typeof gpsPatchSchema>
export const gpsPatchValidator = getValidator(gpsPatchSchema, dataValidator)
export const gpsPatchResolver = resolve<GpsPatch, HookContext>({})

// Schema for allowed query properties - matching original schema
export const gpsQuerySchema = Type.Object({
  ...querySyntax(gpsSchema).properties
}, { additionalProperties: false })

export type GpsQuery = Static<typeof gpsQuerySchema>
export const gpsQueryValidator = getValidator(gpsQuerySchema, queryValidator)
export const gpsQueryResolver = resolve<GpsQuery, HookContext>({})
