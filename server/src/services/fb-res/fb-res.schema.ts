// TypeBox schema for fb-res service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

export const fbResSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Form response fields from original schema
  person: Type.Optional(ObjectIdSchema()),
  form: Type.Optional(ObjectIdSchema()),
  formData: Type.Optional(Type.Any()), // Flexible form data
  lastField: Type.Optional(Type.String()),

  // Common fields
  ...commonFields.properties
}, { additionalProperties: false })

export type FbRes = Static<typeof fbResSchema>
export const fbResValidator = getValidator(fbResSchema, dataValidator)
export const fbResResolver = resolve<FbRes, HookContext>({})
export const fbResExternalResolver = resolve<FbRes, HookContext>({})

export const fbResDataSchema = Type.Object({
  // No required fields for creation per original schema
  ...Type.Partial(Type.Omit(fbResSchema, ['_id'])).properties
}, { additionalProperties: false })

export type FbResData = Static<typeof fbResDataSchema>
export const fbResDataValidator = getValidator(fbResDataSchema, dataValidator)
export const fbResDataResolver = resolve<FbResData, HookContext>({})

export const fbResPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(fbResSchema, ['_id'])).properties,

  // MongoDB operators
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any()))
}, { additionalProperties: false })
export type FbResPatch = Static<typeof fbResPatchSchema>
export const fbResPatchValidator = getValidator(fbResPatchSchema, dataValidator)
export const fbResPatchResolver = resolve<FbResPatch, HookContext>({})

// Schema for allowed query properties - matching original schema
export const fbResQuerySchema = Type.Object({
  ...querySyntax(fbResSchema).properties
}, { additionalProperties: false })

export type FbResQuery = Static<typeof fbResQuerySchema>
export const fbResQueryValidator = getValidator(fbResQuerySchema, queryValidator)
export const fbResQueryResolver = resolve<FbResQuery, HookContext>({})
