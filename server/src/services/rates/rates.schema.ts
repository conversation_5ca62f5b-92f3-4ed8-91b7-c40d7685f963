// TypeBox schema for rates service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

// Premium schema (simplified - would need full import)
const PremiumSchema = Type.Object({
  // Simplified premium fields - full schema would be imported
  individual: Type.Optional(Type.Number()),
  family: Type.Optional(Type.Number()),
  couple: Type.Optional(Type.Number())
}, { additionalProperties: true })

export const ratesSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Required fields from original schema
  coverage: ObjectIdSchema(), // Required
  state: Type.String(), // Required

  // Coverage rate fields from original schema
  stateKey: Type.Optional(Type.String()), // Computed field: coverage:state
  areas: Type.Optional(Type.Array(Type.Object({
    name: Type.Optional(Type.String()),
    zips: Type.Optional(Type.Array(Type.String())),
    premium: Type.Optional(PremiumSchema)
  }))),
  premium: Type.Optional(PremiumSchema),

  // Common fields
  ...commonFields.properties
}, { additionalProperties: false })

export type Rates = Static<typeof ratesSchema>
export const ratesValidator = getValidator(ratesSchema, dataValidator)
export const ratesResolver = resolve<Rates, HookContext>({
  // Resolvers from original schema
  state: async (val) => {
    return val?.toUpperCase()
  },
  stateKey: async (val, data) => {
    return `${data.coverage}:${data.state}`
  }
})
export const ratesExternalResolver = resolve<Rates, HookContext>({})

export const ratesDataSchema = Type.Object({
  // Required fields for creating new rates (only coverage per original)
  coverage: ObjectIdSchema(),

  // All other fields from main schema (optional for creation)
  ...Type.Partial(Type.Omit(ratesSchema, ['_id', 'coverage'])).properties
}, { additionalProperties: false })

export type RatesData = Static<typeof ratesDataSchema>
export const ratesDataValidator = getValidator(ratesDataSchema, dataValidator)
export const ratesDataResolver = resolve<RatesData, HookContext>({})

export const ratesPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(ratesSchema, ['_id'])).properties,

  // MongoDB operators
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any()))
}, { additionalProperties: false })
export type RatesPatch = Static<typeof ratesPatchSchema>
export const ratesPatchValidator = getValidator(ratesPatchSchema, dataValidator)
export const ratesPatchResolver = resolve<RatesPatch, HookContext>({})

// Schema for allowed query properties - matching original schema
export const ratesQuerySchema = Type.Object({
  ...querySyntax(ratesSchema).properties
}, { additionalProperties: false })

export type RatesQuery = Static<typeof ratesQuerySchema>
export const ratesQueryValidator = getValidator(ratesQuerySchema, queryValidator)
export const ratesQueryResolver = resolve<RatesQuery, HookContext>({})
