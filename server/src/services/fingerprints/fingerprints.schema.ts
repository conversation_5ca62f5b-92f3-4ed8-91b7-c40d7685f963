// TypeBox schema for fingerprints service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

// Visit schema for fingerprint visits (flexible schema)
const VisitSchema = Type.Object({}, { additionalProperties: true })

// IP info schema for fingerprint IP data (flexible schema)
const IpInfoSchema = Type.Object({}, { additionalProperties: true })

export const fingerprintsSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Browser fingerprinting fields from original schema
  turnstile: Type.Optional(Type.Object({
    success: Type.Optional(Type.Boolean()),
    challenge_ts: Type.Optional(Type.String()),
    hostname: Type.Optional(Type.String()),
    'error-codes': Type.Optional(Type.Array(Type.Any())),
    action: Type.Optional(Type.String()),
    cdata: Type.Optional(Type.String()),
    metadata: Type.Optional(Type.Object({}, { additionalProperties: true }))
  }, { additionalProperties: true })),

  visitorId: Type.Optional(Type.String()),
  visits: Type.Optional(Type.Array(VisitSchema)),
  name: Type.Optional(Type.String()),
  manufacturer: Type.Optional(Type.String()),
  product: Type.Optional(Type.String()),
  osName: Type.Optional(Type.String()),
  screenWidth: Type.Optional(Type.Any()),
  touch: Type.Optional(Type.Boolean()),
  incognito: Type.Optional(Type.Boolean()),
  type: Type.Optional(Type.String()),
  ua: Type.Optional(Type.String()),
  ipInfo: Type.Optional(IpInfoSchema),

  // Common fields
  ...commonFields.properties
}, { additionalProperties: true })

export type Fingerprints = Static<typeof fingerprintsSchema>
export const fingerprintsValidator = getValidator(fingerprintsSchema, dataValidator)
export const fingerprintsResolver = resolve<Fingerprints, HookContext>({})
export const fingerprintsExternalResolver = resolve<Fingerprints, HookContext>({})

export const fingerprintsDataSchema = Type.Object({
  // No required fields for creation per original schema
  ...Type.Partial(Type.Omit(fingerprintsSchema, ['_id'])).properties
}, { additionalProperties: true })

export type FingerprintsData = Static<typeof fingerprintsDataSchema>
export const fingerprintsDataValidator = getValidator(fingerprintsDataSchema, dataValidator)
export const fingerprintsDataResolver = resolve<FingerprintsData, HookContext>({})

export const fingerprintsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(fingerprintsSchema, ['_id'])).properties,

  // MongoDB operators
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any()))
}, { additionalProperties: true })
export type FingerprintsPatch = Static<typeof fingerprintsPatchSchema>
export const fingerprintsPatchValidator = getValidator(fingerprintsPatchSchema, dataValidator)
export const fingerprintsPatchResolver = resolve<FingerprintsPatch, HookContext>({})

// Schema for allowed query properties - matching original schema
export const fingerprintsQuerySchema = Type.Object({
  ...querySyntax(fingerprintsSchema).properties
}, { additionalProperties: true })

export type FingerprintsQuery = Static<typeof fingerprintsQuerySchema>
export const fingerprintsQueryValidator = getValidator(fingerprintsQuerySchema, queryValidator)
export const fingerprintsQueryResolver = resolve<FingerprintsQuery, HookContext>({})
