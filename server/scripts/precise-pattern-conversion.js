#!/usr/bin/env node

import fs from 'fs'
import path from 'path'
import { execSync } from 'child_process'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)
const serverDir = path.dirname(__dirname)

function toCamelCase(str) {
  return str.replace(/-([a-z])/g, (g) => g[1].toUpperCase())
}

// Extract exact pattern properties from old schema content
function extractPatternProperties(content, serviceName) {
  const patterns = {}
  
  // Find all patternProperties definitions
  const patternMatches = content.match(/(\w+):\s*{[^}]*patternProperties:\s*{[^}]*"([^"]+)":\s*([^}]+(?:\{[^}]*\}[^}]*)*)\s*}/g)
  
  if (patternMatches) {
    patternMatches.forEach(match => {
      // Extract property name, pattern, and value schema
      const propertyMatch = match.match(/(\w+):\s*{[^}]*patternProperties:\s*{\s*"([^"]+)":\s*(.*)\s*}/)
      if (propertyMatch) {
        const [, propertyName, pattern, valueSchema] = propertyMatch
        patterns[propertyName] = {
          pattern,
          valueSchema: valueSchema.trim()
        }
      }
    })
  }
  
  return patterns
}

// Convert JSON Schema value to TypeBox equivalent
function convertValueSchemaToTypeBox(valueSchema) {
  // Handle simple types
  if (valueSchema.includes('{type: \'string\'}')) {
    return 'Type.String()'
  }
  if (valueSchema.includes('{type: \'number\'}')) {
    return 'Type.Number()'
  }
  if (valueSchema.includes('{type: \'boolean\'}')) {
    return 'Type.Boolean()'
  }
  if (valueSchema.includes('ObjectIdSchema()')) {
    return 'ObjectIdSchema()'
  }
  
  // Handle references to other schemas
  if (valueSchema.includes('dedSchema')) {
    return 'DedSchema'
  }
  if (valueSchema.includes('imageSchema')) {
    return 'ImageSchema'
  }
  
  // Handle complex objects - extract properties
  const objectMatch = valueSchema.match(/type:\s*'object',\s*properties:\s*{([^}]+(?:\{[^}]*\}[^}]*)*)}/)
  if (objectMatch) {
    const propertiesContent = objectMatch[1]
    const properties = []
    
    // Extract individual properties
    const propMatches = propertiesContent.match(/(\w+):\s*({[^}]*}|ObjectIdSchema\(\)|{type:\s*'[^']+'})/g)
    if (propMatches) {
      propMatches.forEach(propMatch => {
        const [, propName, propType] = propMatch.match(/(\w+):\s*(.+)/)
        let typeBoxType = 'Type.Any()'
        
        if (propType.includes('ObjectIdSchema()')) {
          typeBoxType = 'ObjectIdSchema()'
        } else if (propType.includes('type: \'string\'')) {
          typeBoxType = 'Type.String()'
        } else if (propType.includes('type: \'number\'')) {
          typeBoxType = 'Type.Number()'
        } else if (propType.includes('type: \'boolean\'')) {
          typeBoxType = 'Type.Boolean()'
        } else if (propType.includes('type: \'array\'')) {
          if (propType.includes('ObjectIdSchema()')) {
            typeBoxType = 'Type.Array(ObjectIdSchema())'
          } else if (propType.includes('type: \'string\'')) {
            typeBoxType = 'Type.Array(Type.String())'
          } else {
            typeBoxType = 'Type.Array(Type.Any())'
          }
        } else if (propType.includes('enum:')) {
          const enumMatch = propType.match(/enum:\s*\[([^\]]+)\]/)
          if (enumMatch) {
            const enumValues = enumMatch[1].split(',').map(v => v.trim().replace(/'/g, ''))
            typeBoxType = `Type.Union([${enumValues.map(v => `Type.Literal('${v}')`).join(', ')}])`
          }
        }
        
        properties.push(`${propName}: ${typeBoxType}`)
      })
    }
    
    return `Type.Object({ ${properties.join(', ')} })`
  }
  
  // Fallback
  return 'Type.Any()'
}

// Convert pattern properties to TypeBox Record
function convertPatternToTypeBox(pattern, valueSchema) {
  const typeBoxValue = convertValueSchemaToTypeBox(valueSchema)
  
  // All patterns use string keys in practice
  return `Type.Record(Type.String(), ${typeBoxValue})`
}

function restorePatternPropertiesForService(serviceName) {
  try {
    // Get old schema content
    const oldSchemaContent = execSync(`git show redis-on:server/src/services/${serviceName}/${serviceName}.schema.ts`, { encoding: 'utf8' })
    
    // Extract pattern properties
    const patterns = extractPatternProperties(oldSchemaContent, serviceName)
    
    if (Object.keys(patterns).length === 0) {
      console.log(`⚠️  ${serviceName}: No pattern properties found`)
      return false
    }
    
    console.log(`🔧 ${serviceName}: Found ${Object.keys(patterns).length} pattern properties`)
    
    // Get current TypeBox schema
    const schemaPath = path.join(serverDir, 'src', 'services', serviceName, `${serviceName}.schema.ts`)
    if (!fs.existsSync(schemaPath)) {
      console.log(`❌ ${serviceName}: TypeBox schema file not found`)
      return false
    }
    
    let content = fs.readFileSync(schemaPath, 'utf8')
    const camelName = toCamelCase(serviceName)
    
    // Find the main schema definition
    const schemaPattern = new RegExp(`(export const ${camelName}Schema = Type\\.Object\\({[\\s\\S]*?)(}\\s*,\\s*{[\\s\\S]*?}\\))`)
    const schemaMatch = content.match(schemaPattern)
    
    if (!schemaMatch) {
      console.log(`❌ ${serviceName}: Could not find schema pattern`)
      return false
    }
    
    let schemaContent = schemaMatch[1]
    const schemaEnd = schemaMatch[2]
    
    // Remove any existing pattern properties that we're replacing
    Object.keys(patterns).forEach(propName => {
      const regex = new RegExp(`\\s*${propName}:\\s*[^,]+,?`, 'g')
      schemaContent = schemaContent.replace(regex, '')
    })
    
    // Add pattern properties as TypeBox Records
    const patternPropsStr = Object.entries(patterns).map(([propName, config]) => {
      const typeBoxRecord = convertPatternToTypeBox(config.pattern, config.valueSchema)
      return `  ${propName}: Type.Optional(${typeBoxRecord}),`
    }).join('\n')
    
    // Insert pattern properties before the closing brace
    const newSchemaContent = schemaContent + ',\n  // Pattern Properties (dynamic keys)\n' + patternPropsStr + '\n' + schemaEnd
    
    content = content.replace(schemaMatch[0], newSchemaContent)
    
    fs.writeFileSync(schemaPath, content)
    console.log(`✅ ${serviceName}: Restored ${Object.keys(patterns).length} pattern properties as TypeBox Records`)
    
    // Log what was converted for verification
    Object.entries(patterns).forEach(([propName, config]) => {
      console.log(`   ${propName}: ${config.pattern} -> ${convertPatternToTypeBox(config.pattern, config.valueSchema)}`)
    })
    
    return true
    
  } catch (error) {
    console.log(`❌ ${serviceName}: Error - ${error.message}`)
    return false
  }
}

// Services that had pattern properties (from your list)
const servicesWithPatterns = [
  'coverages', 'ppls', 'caps', 'wallets', 'bills', 'orgs', 
  'se-plans', 'shops', 'gps', 'claims', 'plans', 'hosts'
]

console.log(`🔍 Precisely restoring pattern properties for ${servicesWithPatterns.length} services...\n`)

let successCount = 0
servicesWithPatterns.forEach(serviceName => {
  if (restorePatternPropertiesForService(serviceName)) {
    successCount++
  }
})

console.log(`\n📊 Results:`)
console.log(`✅ Successfully restored: ${successCount}/${servicesWithPatterns.length} services`)
console.log(`🎯 Pattern properties converted to TypeBox Type.Record() equivalents`)

if (successCount > 0) {
  console.log(`\n🔍 Run compilation test to verify the conversions`)
}
