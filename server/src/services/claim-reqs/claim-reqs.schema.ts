// TypeBox schema for claim-reqs service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, ImageSchema } from '../../utils/common/typebox-schemas.js'

// Import claims schema for claimData reference
// Note: This creates a circular dependency that needs to be handled carefully
// For now, using Type.Any() for claimData to avoid circular import

export const claimReqsSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Claim request entities - all ObjectIds
  plan: Type.Optional(ObjectIdSchema()),
  org: Type.Optional(ObjectIdSchema()),
  patient: Type.Optional(ObjectIdSchema()),
  person: Type.Optional(ObjectIdSchema()),
  care: Type.Optional(ObjectIdSchema()),
  visit: Type.Optional(ObjectIdSchema()),
  claim: Type.Optional(ObjectIdSchema()),
  provider: Type.Optional(ObjectIdSchema()),
  practitioner: Type.Optional(ObjectIdSchema()),
  providerOrg: Type.Optional(ObjectIdSchema()),

  // Arrays
  threads: Type.Optional(Type.Array(ObjectIdSchema())),

  // Claim request configuration
  status: Type.Optional(Type.Union([
    Type.Literal('unopened'), Type.Literal('pending'),
    Type.Literal('approved'), Type.Literal('rejected')
  ])),
  removeRequest: Type.Optional(Type.Boolean()),

  // Complex claim data object (avoiding circular dependency with claims schema)
  claimData: Type.Optional(Type.Record(Type.String(), Type.Any())), // Should be {...claimsSchema.properties}

  // CRITICAL Pattern Property - This was completely lost!
  files: Type.Optional(Type.Record(Type.String(), ImageSchema)), // Pattern: ^.*$

  // Common fields
  ...commonFields.properties
}, { additionalProperties: false })

export type ClaimReqs = Static<typeof claimReqsSchema>
export const claimReqsValidator = getValidator(claimReqsSchema, dataValidator)
export const claimReqsResolver = resolve<ClaimReqs, HookContext>({})
export const claimReqsExternalResolver = resolve<ClaimReqs, HookContext>({})

export const claimReqsDataSchema = Type.Object({
  ...Type.Partial(Type.Omit(claimReqsSchema, ['_id'])).properties
}, { additionalProperties: false })

export type ClaimReqsData = Static<typeof claimReqsDataSchema>
export const claimReqsDataValidator = getValidator(claimReqsDataSchema, dataValidator)
export const claimReqsDataResolver = resolve<ClaimReqsData, HookContext>({
  // Default status resolver from original schema
  status: async (val) => {
    if (!val) return 'unopened'
    return val
  }
})

export const claimReqsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(claimReqsSchema, ['_id'])).properties,

  // MongoDB operators
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any()))
}, { additionalProperties: false })
export type ClaimReqsPatch = Static<typeof claimReqsPatchSchema>
export const claimReqsPatchValidator = getValidator(claimReqsPatchSchema, dataValidator)
export const claimReqsPatchResolver = resolve<ClaimReqsPatch, HookContext>({})

// Schema for allowed query properties - matching original schema
export const claimReqsQuerySchema = Type.Object({
  ...querySyntax(claimReqsSchema).properties
}, { additionalProperties: false })

export type ClaimReqsQuery = Static<typeof claimReqsQuerySchema>
export const claimReqsQueryValidator = getValidator(claimReqsQuerySchema, queryValidator)
export const claimReqsQueryResolver = resolve<ClaimReqsQuery, HookContext>({})
