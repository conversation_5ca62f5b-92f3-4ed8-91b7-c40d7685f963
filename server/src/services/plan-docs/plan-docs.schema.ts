// TypeBox schema for plan-docs service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

// Plan class enum from original schema
export const PlanClassSchema = Type.Union([
  Type.Literal('core'), Type.Literal('125'), Type.Literal('105'),
  Type.Literal('misc'), Type.Literal('spd')
])

// Sections schema with NESTED pattern properties from original schema
export const SectionsSchema = Type.Record(
  Type.String({ pattern: '^([0-9]|[1-9][0-9]|1[01][0-9]|120)$' }), // Pattern: section numbers 0-120
  Type.Object({
    key: Type.Optional(Type.String()),
    title: Type.Optional(Type.String()),
    sections: Type.Optional(Type.Record(Type.String(), Type.Object({
      title: Type.Optional(Type.String()),
      body: Type.Optional(Type.String())
    }))) // NESTED Pattern: ^.*$
  })
)

export const planDocsSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Required fields from original schema
  plan: ObjectIdSchema(), // Required
  name: Type.String(), // Required

  // Plan document fields from original schema
  smb: Type.Optional(Type.Boolean()),
  public: Type.Optional(Type.Boolean()),
  printCount: Type.Optional(Type.Number()),
  description: Type.Optional(Type.String()),
  class: Type.Optional(PlanClassSchema),
  subClass: Type.Optional(Type.String()),
  path: Type.Optional(Type.String()),
  sectionsUpdatedAt: Type.Optional(Type.Any()),
  template: Type.Optional(ObjectIdSchema()),

  // CRITICAL Pattern Property - This was completely lost!
  sections: Type.Optional(SectionsSchema), // NESTED pattern properties!

  // Common fields
  ...commonFields.properties
}, { additionalProperties: false })

export type PlanDocs = Static<typeof planDocsSchema>
export const planDocsValidator = getValidator(planDocsSchema, dataValidator)
export const planDocsResolver = resolve<PlanDocs, HookContext>({})
export const planDocsExternalResolver = resolve<PlanDocs, HookContext>({})

export const planDocsDataSchema = Type.Object({
  // Required fields for creating new plan docs (only plan per original)
  plan: ObjectIdSchema(),

  // All other fields from main schema (optional for creation)
  ...Type.Partial(Type.Omit(planDocsSchema, ['_id', 'plan'])).properties
}, { additionalProperties: false })

export type PlanDocsData = Static<typeof planDocsDataSchema>
export const planDocsDataValidator = getValidator(planDocsDataSchema, dataValidator)
export const planDocsDataResolver = resolve<PlanDocsData, HookContext>({})

export const planDocsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(planDocsSchema, ['_id'])).properties,

  // MongoDB operators
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any()))
}, { additionalProperties: false })
export type PlanDocsPatch = Static<typeof planDocsPatchSchema>
export const planDocsPatchValidator = getValidator(planDocsPatchSchema, dataValidator)
export const planDocsPatchResolver = resolve<PlanDocsPatch, HookContext>({})

// Schema for allowed query properties - matching original schema
export const planDocsQuerySchema = Type.Object({
  ...querySyntax(planDocsSchema).properties
}, { additionalProperties: false })

export type PlanDocsQuery = Static<typeof planDocsQuerySchema>
export const planDocsQueryValidator = getValidator(planDocsQuerySchema, queryValidator)
export const planDocsQueryResolver = resolve<PlanDocsQuery, HookContext>({})
