// TypeBox schema for drops service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, addToSet, pull } from '../../utils/common/typebox-schemas.js'

export const dropsSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Q&A/Forum fields
  tags: Type.Optional(Type.Array(Type.String())),
  type: Type.Optional(Type.Union([Type.Literal('question'), Type.Literal('answer')])),
  archives: Type.Optional(Type.Object({
    title: Type.Optional(Type.Array(Type.String())),
    body: Type.Optional(Type.Array(Type.String()))
  })),
  topAnswer: Type.Optional(ObjectIdSchema()),
  topScore: Type.Optional(Type.Number()),
  title: Type.Optional(Type.String()),
  body: Type.Optional(Type.String()),
  class: Type.Optional(Type.Union([
    Type.Literal('individual'), Type.Literal('business'), Type.Literal('provider')
  ])),
  anonymous: Type.Optional(Type.Boolean()),
  threads: Type.Optional(Type.Array(ObjectIdSchema())),
  voteCount: Type.Optional(Type.Number()),
  upVotes: Type.Optional(Type.Array(ObjectIdSchema())),
  downVotes: Type.Optional(Type.Array(ObjectIdSchema())),

  // Common fields
  ...commonFields.properties
}, { additionalProperties: false })

export type Drops = Static<typeof dropsSchema>
export const dropsValidator = getValidator(dropsSchema, dataValidator)
export const dropsResolver = resolve<Drops, HookContext>({})
export const dropsExternalResolver = resolve<Drops, HookContext>({})

export const dropsDataSchema = Type.Object({
  ...Type.Partial(Type.Omit(dropsSchema, ['_id'])).properties
}, { additionalProperties: false })

export type DropsData = Static<typeof dropsDataSchema>
export const dropsDataValidator = getValidator(dropsDataSchema, dataValidator)
export const dropsDataResolver = resolve<DropsData, HookContext>({})

export const dropsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(dropsSchema, ['_id'])).properties,

  // MongoDB operators - matching original pushPull for threads
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $addToSet: Type.Optional(addToSet([
    { path: 'threads', type: ObjectIdSchema() },
    { path: 'upVotes', type: ObjectIdSchema() },
    { path: 'downVotes', type: ObjectIdSchema() }
  ])),
  $pull: Type.Optional(pull([
    { path: 'threads', type: ObjectIdSchema() },
    { path: 'upVotes', type: ObjectIdSchema() },
    { path: 'downVotes', type: ObjectIdSchema() }
  ]))
}, { additionalProperties: false })
export type DropsPatch = Static<typeof dropsPatchSchema>
export const dropsPatchValidator = getValidator(dropsPatchSchema, dataValidator)
export const dropsPatchResolver = resolve<DropsPatch, HookContext>({})

// Schema for allowed query properties - matching original schema
export const dropsQuerySchema = Type.Object({
  ...querySyntax(dropsSchema).properties
}, { additionalProperties: false })

export type DropsQuery = Static<typeof dropsQuerySchema>
export const dropsQueryValidator = getValidator(dropsQuerySchema, queryValidator)
export const dropsQueryResolver = resolve<DropsQuery, HookContext>({})
