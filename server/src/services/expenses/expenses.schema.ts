// TypeBox schema for expenses service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, addToSet, pull } from '../../utils/common/typebox-schemas.js'

// Closed limit schema for expense limits
const ClosedLimitSchema = Type.Object({
  id: Type.Optional(Type.String()),
  amount: Type.Optional(Type.Number()),
  closedAt: Type.Optional(Type.Any()),
  reason: Type.Optional(Type.String())
}, { additionalProperties: false })

export const expensesSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Required fields from original schema
  name: Type.String(), // Required
  owner: ObjectIdSchema(), // Required
  budget: ObjectIdSchema(), // Required
  limit_owner: ObjectIdSchema(), // Required

  // Expense limit configuration
  amount: Type.Optional(Type.Number()), // spend limit
  max_transaction: Type.Optional(Type.Number()), // max transaction limit
  recurs: Type.Optional(Type.Number()), // monthly limit
  recur_start: Type.Optional(Type.String()),
  next_reset: Type.Optional(Type.String()),
  lock_date: Type.Optional(Type.String()),
  spent: Type.Optional(Type.Number()), // amount spent
  spent_pending: Type.Optional(Type.Number()), // amount pending - moves to zero when spent confirmed
  singleUse: Type.Optional(Type.Boolean()),
  singleVendor: Type.Optional(Type.Boolean()),
  perTransactionLimit: Type.Optional(Type.Number()),

  // Arrays of related entities
  approvers: Type.Optional(Type.Array(ObjectIdSchema())),
  closedLimitIds: Type.Optional(Type.Array(Type.String())),
  closedLimits: Type.Optional(Type.Array(ClosedLimitSchema)),
  managers: Type.Optional(Type.Array(ObjectIdSchema())),
  ramp_whitelist: Type.Optional(Type.Array(Type.Number())),
  ramp_blacklist: Type.Optional(Type.Array(Type.Number())),
  vendor_whitelist: Type.Optional(Type.Array(ObjectIdSchema())), // orgs that a limit is limited to - must have a ramp_vendor_id
  vendor_blacklist: Type.Optional(Type.Array(ObjectIdSchema())), // orgs that a limit is restricted for - must have a ramp_vendor_id
  mcc_whitelist: Type.Optional(Type.Array(Type.String())),
  mcc_blacklist: Type.Optional(Type.Array(Type.String())),
  members: Type.Optional(Type.Array(ObjectIdSchema())),
  users: Type.Optional(Type.Array(ObjectIdSchema())),

  // Configuration
  last4: Type.Optional(Type.String()),
  lastSync: Type.Optional(Type.Any()),
  ramp_limit: Type.Optional(Type.String()),
  preAuth: Type.Optional(Type.Number()),
  status: Type.Optional(Type.Union([
    Type.Literal('active'), Type.Literal('inactive'),
    Type.Literal('canceled'), Type.Literal('pending-verification')
  ])),

  // Common fields
  ...commonFields.properties
}, { additionalProperties: false })

export type Expenses = Static<typeof expensesSchema>
export const expensesValidator = getValidator(expensesSchema, dataValidator)
export const expensesResolver = resolve<Expenses, HookContext>({})
export const expensesExternalResolver = resolve<Expenses, HookContext>({})

export const expensesDataSchema = Type.Object({
  // No required fields for creation per original schema (excludes amount, recurs per createPatchSchema)
  ...Type.Partial(Type.Omit(expensesSchema, ['_id', 'amount', 'recurs'])).properties
}, { additionalProperties: false })

export type ExpensesData = Static<typeof expensesDataSchema>
export const expensesDataValidator = getValidator(expensesDataSchema, dataValidator)
export const expensesDataResolver = resolve<ExpensesData, HookContext>({})

export const expensesPatchSchema = Type.Object({
  // Exclude amount and recurs per original createPatchSchema
  ...Type.Partial(Type.Omit(expensesSchema, ['_id', 'amount', 'recurs'])).properties,

  // MongoDB operators - matching original $inc, $addToSet, $pull with listArgs
  $inc: Type.Optional(Type.Record(Type.String(), Type.Number())),
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $addToSet: Type.Optional(addToSet([
    { path: 'approvers', type: ObjectIdSchema() },
    { path: 'managers', type: ObjectIdSchema() },
    { path: 'members', type: ObjectIdSchema() },
    { path: 'users', type: ObjectIdSchema() },
    { path: 'vendor_whitelist', type: ObjectIdSchema() },
    { path: 'vendor_blacklist', type: ObjectIdSchema() },
    { path: 'mcc_whitelist', type: Type.String() },
    { path: 'mcc_blacklist', type: Type.String() },
    { path: 'ramp_whitelist', type: Type.Number() },
    { path: 'ramp_blacklist', type: Type.Number() },
    { path: 'closedLimitIds', type: Type.String() },
    { path: 'closedLimits', type: ClosedLimitSchema }
  ])),
  $pull: Type.Optional(pull([
    { path: 'approvers', type: ObjectIdSchema() },
    { path: 'managers', type: ObjectIdSchema() },
    { path: 'members', type: ObjectIdSchema() },
    { path: 'users', type: ObjectIdSchema() },
    { path: 'vendor_whitelist', type: ObjectIdSchema() },
    { path: 'vendor_blacklist', type: ObjectIdSchema() },
    { path: 'mcc_whitelist', type: Type.String() },
    { path: 'mcc_blacklist', type: Type.String() },
    { path: 'ramp_whitelist', type: Type.Number() },
    { path: 'ramp_blacklist', type: Type.Number() },
    { path: 'closedLimitIds', type: Type.String() },
    { path: 'closedLimits', type: ClosedLimitSchema }
  ]))
}, { additionalProperties: false })
export type ExpensesPatch = Static<typeof expensesPatchSchema>
export const expensesPatchValidator = getValidator(expensesPatchSchema, dataValidator)
export const expensesPatchResolver = resolve<ExpensesPatch, HookContext>({})

// Schema for allowed query properties - matching original schema
export const expensesQuerySchema = Type.Object({
  ...querySyntax(expensesSchema).properties
}, { additionalProperties: false })

export type ExpensesQuery = Static<typeof expensesQuerySchema>
export const expensesQueryValidator = getValidator(expensesQuerySchema, queryValidator)
export const expensesQueryResolver = resolve<ExpensesQuery, HookContext>({})
