// TypeBox schema for pings service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

// Simplified send schema (would need full import in real implementation)
const SendSchema = Type.Object({
  send: Type.Optional(Type.Boolean()),
  opened: Type.Optional(Type.Boolean()),
  sentAt: Type.Optional(Type.Any()),
  openedAt: Type.Optional(Type.Any())
}, { additionalProperties: true })

export const pingsSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Notification fields from original schema
  subject: Type.Optional(ObjectIdSchema()),
  subjectService: Type.Optional(Type.String()),
  recipient: Type.Optional(ObjectIdSchema()),
  recipientService: Type.Optional(Type.String()), // should be ppls
  nouns: Type.Optional(Type.Array(Type.String())), // who did something
  verbs: Type.Optional(Type.Array(Type.String())), // what they did
  message: Type.Optional(Type.String()),
  subjectAvatarPath: Type.Optional(Type.String()),
  subjectNamePath: Type.Optional(Type.String()),
  link: Type.Optional(Type.String()),
  tries: Type.Optional(Type.Number()), // use for patching
  action: Type.Optional(Type.String()), // categorize what happened
  priority: Type.Optional(Type.Number()), // lower is higher, <5 internal, <3 email
  category: Type.Optional(Type.String()), // general category for notification management
  methods: Type.Optional(Type.Object({
    internal: Type.Optional(SendSchema),
    email: Type.Optional(SendSchema),
    sms: Type.Optional(SendSchema)
  })),

  // Common fields
  ...commonFields.properties
}, { additionalProperties: false })

export type Pings = Static<typeof pingsSchema>
export const pingsValidator = getValidator(pingsSchema, dataValidator)
export const pingsResolver = resolve<Pings, HookContext>({})
export const pingsExternalResolver = resolve<Pings, HookContext>({})

export const pingsDataSchema = Type.Object({
  // No required fields for creation per original schema
  ...Type.Partial(Type.Omit(pingsSchema, ['_id'])).properties
}, { additionalProperties: false })

export type PingsData = Static<typeof pingsDataSchema>
export const pingsDataValidator = getValidator(pingsDataSchema, dataValidator)
export const pingsDataResolver = resolve<PingsData, HookContext>({
  // Critical resolvers from original schema
  recipientService: async (val) => {
    return val || 'logins'
  },
  category: async (val) => {
    return val || 'general'
  },
  priority: async (val) => {
    if ((val || val === 0) && val > -1) return val
    return 10
  },
  methods: async (val) => {
    return {
      internal: { send: true },
      email: { send: false },
      sms: { send: false },
      ...val
    }
  }
})

export const pingsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(pingsSchema, ['_id'])).properties,

  // Custom patch property from original schema for notification tracking
  'methods.internal.opened': Type.Optional(Type.Boolean()),

  // MongoDB operators
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any()))
}, { additionalProperties: false })
export type PingsPatch = Static<typeof pingsPatchSchema>
export const pingsPatchValidator = getValidator(pingsPatchSchema, dataValidator)
export const pingsPatchResolver = resolve<PingsPatch, HookContext>({})

// Schema for allowed query properties - matching original schema with commonQueries
export const pingsQuerySchema = Type.Object({
  ...querySyntax(pingsSchema).properties
  // Note: commonQueries.properties would be added here in full implementation
}, { additionalProperties: true })

export type PingsQuery = Static<typeof pingsQuerySchema>
export const pingsQueryValidator = getValidator(pingsQuerySchema, queryValidator)
export const pingsQueryResolver = resolve<PingsQuery, HookContext>({})
