// TypeBox schema for errs service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

export const errsSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Error logging fields from original schema
  path: Type.Optional(Type.String()),
  method: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  id: Type.Optional(Type.String()),
  error: Type.Optional(Type.Any()),
  params: Type.Optional(Type.Any()),
  data: Type.Optional(Type.Any()),
  result: Type.Optional(Type.Any()),

  // Common fields
  ...commonFields.properties
}, { additionalProperties: false })

export type Errs = Static<typeof errsSchema>
export const errsValidator = getValidator(errsSchema, dataValidator)
export const errsResolver = resolve<Errs, HookContext>({})
export const errsExternalResolver = resolve<Errs, HookContext>({})

export const errsDataSchema = Type.Object({
  ...Type.Partial(Type.Omit(errsSchema, ['_id'])).properties
}, { additionalProperties: false })

export type ErrsData = Static<typeof errsDataSchema>
export const errsDataValidator = getValidator(errsDataSchema, dataValidator)
export const errsDataResolver = resolve<ErrsData, HookContext>({})

export const errsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(errsSchema, ['_id'])).properties,

  // MongoDB operators
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any()))
}, { additionalProperties: false })
export type ErrsPatch = Static<typeof errsPatchSchema>
export const errsPatchValidator = getValidator(errsPatchSchema, dataValidator)
export const errsPatchResolver = resolve<ErrsPatch, HookContext>({})

// Schema for allowed query properties - matching original schema
export const errsQuerySchema = Type.Object({
  ...querySyntax(errsSchema).properties
}, { additionalProperties: false })

export type ErrsQuery = Static<typeof errsQuerySchema>
export const errsQueryValidator = getValidator(errsQuerySchema, queryValidator)
export const errsQueryResolver = resolve<ErrsQuery, HookContext>({})
