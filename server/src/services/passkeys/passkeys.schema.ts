// TypeBox schema for passkeys service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

// Constants from original schema
export const challengeKinds = ['registration', 'authentication'] as const
const base64urlPattern = '^[A-Za-z0-9_-]+$' as const
const transportEnum = ['usb', 'nfc', 'ble', 'internal', 'hybrid', 'cable'] as const

export const passkeysSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Required fields from original schema
  login: ObjectIdSchema(), // Required
  rpID: Type.String(), // Required - Relying Party ID, e.g. example.com
  credentialId: Type.String({ pattern: base64urlPattern }), // Required - WebAuthnCredential.id (base64url)
  publicKey: Type.String({ pattern: base64urlPattern }), // Required - WebAuthnCredential.publicKey encoded to base64url
  signCount: Type.Number(), // Required - Verification counter (may not always increase)

  // WebAuthn passkey fields
  transports: Type.Optional(Type.Array(Type.Union([
    Type.Literal('usb'), Type.Literal('nfc'), Type.Literal('ble'),
    Type.Literal('internal'), Type.Literal('hybrid'), Type.Literal('cable')
  ]))), // Authenticator transport hints
  aaguid: Type.Optional(Type.String()), // Authenticator AAGUID (UUID string if provided)

  // Modern backup metadata from v13 verification
  backupEligible: Type.Optional(Type.Boolean()), // Derived from credentialDeviceType === "multiDevice"
  backupState: Type.Optional(Type.Union([Type.Literal('enabled'), Type.Literal('disabled')])), // Derived from credentialBackedUp
  displayName: Type.Optional(Type.String()), // User-visible label for the device

  // Common fields
  ...commonFields.properties
}, { additionalProperties: false })

export type Passkeys = Static<typeof passkeysSchema>
export const passkeysValidator = getValidator(passkeysSchema, dataValidator)
export const passkeysResolver = resolve<Passkeys, HookContext>({})
export const passkeysExternalResolver = resolve<Passkeys, HookContext>({})

export const passkeysDataSchema = Type.Object({
  // No required fields for creation per original schema
  ...Type.Partial(Type.Omit(passkeysSchema, ['_id'])).properties
}, { additionalProperties: false })

export type PasskeysData = Static<typeof passkeysDataSchema>
export const passkeysDataValidator = getValidator(passkeysDataSchema, dataValidator)
export const passkeysDataResolver = resolve<PasskeysData, HookContext>({})

export const passkeysPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(passkeysSchema, ['_id'])).properties,

  // MongoDB operators
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any()))
}, { additionalProperties: false })
export type PasskeysPatch = Static<typeof passkeysPatchSchema>
export const passkeysPatchValidator = getValidator(passkeysPatchSchema, dataValidator)
export const passkeysPatchResolver = resolve<PasskeysPatch, HookContext>({})

// Schema for allowed query properties - matching original schema
export const passkeysQuerySchema = Type.Object({
  ...querySyntax(passkeysSchema).properties
}, { additionalProperties: false })

export type PasskeysQuery = Static<typeof passkeysQuerySchema>
export const passkeysQueryValidator = getValidator(passkeysQuerySchema, queryValidator)
export const passkeysQueryResolver = resolve<PasskeysQuery, HookContext>({})
