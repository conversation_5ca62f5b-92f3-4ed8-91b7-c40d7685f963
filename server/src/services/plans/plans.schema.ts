// TypeBox schema for plans service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, addToSet, pull } from '../../utils/common/typebox-schemas.js'

// Contact info schema for plan contacts
const ContactInfoSchema = Type.Object({
  name: Type.Optional(Type.String()),
  email: Type.Optional(Type.String()),
  phone: Type.Optional(Type.String()),
  address: Type.Optional(Type.String())
}, { additionalProperties: false })

// RFP schema for plan RFP sections
const RfpSchema = Type.Object({
  content: Type.Optional(Type.String()),
  updatedAt: Type.Optional(Type.Any())
}, { additionalProperties: false })

// Contributions schema for enrollment contributions
const ContributionsSchema = Type.Object({
  single: Type.Optional(Type.Number()),
  family: Type.Optional(Type.Number())
}, { additionalProperties: false })

// Simplified cafe and hra schemas (avoiding circular dependencies)
const CafeSchema = Type.Object({
  enabled: Type.Optional(Type.Boolean()),
  amount: Type.Optional(Type.Number())
}, { additionalProperties: false })

const HraSchema = Type.Object({
  enabled: Type.Optional(Type.Boolean()),
  amount: Type.Optional(Type.Number())
}, { additionalProperties: false })

// Main data model schema
export const plansSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Required fields from original schema
  name: Type.String(), // Required

  // Plan collaboration fields from original schema
  org: Type.Optional(ObjectIdSchema()),
  parent: Type.Optional(ObjectIdSchema()),
  doc: Type.Optional(ObjectIdSchema()),
  spd: Type.Optional(ObjectIdSchema()),
  ale: Type.Optional(Type.Boolean()),
  estFte: Type.Optional(Type.Number()),
  groups: Type.Optional(Type.Array(ObjectIdSchema())),
  orgs: Type.Optional(Type.Array(ObjectIdSchema())),

  // CRITICAL Pattern Properties - All 4 were completely lost!
  vectorStoreIds: Type.Optional(Type.Record(Type.String(), Type.Object({
    id: Type.Optional(Type.String()), // Vector store id
    fileIds: Type.Optional(Type.Array(Type.String())), // file id so they can be removed upon update
    updatedAt: Type.Optional(Type.Any())
  }))), // Pattern: ^.*$

  team: Type.Optional(Type.Record(Type.String(), Type.Object({
    id: Type.Optional(ObjectIdSchema()),
    status: Type.Optional(Type.Union([Type.Literal('pending'), Type.Literal('canceled'), Type.Literal('active')])),
    role: Type.Optional(Type.String()), // guideEnum
    roleDescription: Type.Optional(Type.String()),
    fee: Type.Optional(Type.Number()),
    feeType: Type.Optional(Type.Union([Type.Literal('alg'), Type.Literal('pepm'), Type.Literal('pmpm'), Type.Literal('flat')])),
    feeDescription: Type.Optional(Type.String()),
    contract: Type.Optional(ObjectIdSchema()),
    conflicts: Type.Optional(Type.String()),
    approvedBy: Type.Optional(ObjectIdSchema()),
    approvedAt: Type.Optional(Type.Any())
  }))), // Pattern: ^.*$

  enrollments: Type.Optional(Type.Record(Type.String(), Type.Object({
    active: Type.Optional(Type.Boolean()),
    description: Type.Optional(Type.String()),
    open: Type.Optional(Type.String()),
    close: Type.Optional(Type.String()),
    enrolled: Type.Optional(Type.Number()),
    lastUpdate: Type.Optional(Type.Any()),
    lastEnrolled: Type.Optional(Type.String()),
    contributions: Type.Optional(ContributionsSchema),
    open_enroll: Type.Optional(Type.Boolean()),
    ppls: Type.Optional(Type.Array(ObjectIdSchema())),
    groups: Type.Optional(Type.Array(ObjectIdSchema())),
    sentThrough: Type.Optional(Type.Number())
  }))), // Pattern: ^.*$

  coverages: Type.Optional(Type.Record(Type.String(), Type.Object({
    type: Type.Optional(Type.String()),
    groups: Type.Optional(Type.Array(ObjectIdSchema())),
    budget: Type.Optional(ObjectIdSchema()),
    card: Type.Optional(ObjectIdSchema()),
    employeeBudget: Type.Optional(Type.Boolean()),
    employerContribution: Type.Optional(Type.Object({
      single: Type.Optional(Type.Number()),
      family: Type.Optional(Type.Number())
    }))
  }))), // Pattern: ^.*$

  // Complex objects from original schema
  rfp: Type.Optional(Type.Object({
    'care_director': Type.Optional(RfpSchema),
    'plan_guide': Type.Optional(RfpSchema),
    'compliance': Type.Optional(RfpSchema),
    'finance': Type.Optional(RfpSchema),
    'physician': Type.Optional(RfpSchema)
  })),

  dependents: Type.Optional(Type.Object({
    excludeSpouse: Type.Optional(Type.Boolean())
  })),

  info: Type.Optional(Type.Object({
    sponsor: Type.Optional(Type.Object({
      ...ContactInfoSchema.properties,
      ein: Type.Optional(Type.String())
    })),
    planAdmin: Type.Optional(ContactInfoSchema),
    fiduciary: Type.Optional(ContactInfoSchema),
    legalAgent: Type.Optional(ContactInfoSchema),
    number: Type.Optional(Type.String()),
    numberEin: Type.Optional(Type.String())
  }, { additionalProperties: true })),

  // Additional fields from original schema
  template: Type.Optional(Type.Boolean()),
  aka: Type.Optional(Type.Array(Type.String())),
  active: Type.Optional(Type.Boolean()),
  description: Type.Optional(Type.String()),
  eligibility: Type.Optional(Type.Object({
    hours: Type.Optional(Type.Number()),
    term: Type.Optional(Type.Number())
  })),
  billEraser: Type.Optional(Type.Object({
    max: Type.Optional(Type.Number()),
    budget: Type.Optional(ObjectIdSchema())
  })),
  planYearStart: Type.Optional(Type.Any()),
  cafe: Type.Optional(CafeSchema),
  hra: Type.Optional(HraSchema),

  // Common fields
  ...commonFields.properties
}, { additionalProperties: false })

export type Plans = Static<typeof plansSchema>
export const plansValidator = getValidator(plansSchema, dataValidator)
export const plansResolver = resolve<Plans, HookContext>({})
export const plansExternalResolver = resolve<Plans, HookContext>({})

// Schema for creating new data
export const plansDataSchema = Type.Object({
  // No required fields for creation per original schema
  ...Type.Partial(Type.Omit(plansSchema, ['_id'])).properties
}, { additionalProperties: false })

export type PlansData = Static<typeof plansDataSchema>
export const plansDataValidator = getValidator(plansDataSchema, dataValidator)
export const plansDataResolver = resolve<PlansData, HookContext>({})

// Schema for updating existing data
export const plansPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(plansSchema, ['_id'])).properties,

  // MongoDB operators
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $inc: Type.Optional(Type.Record(Type.String(), Type.Number())),
  $addToSet: Type.Optional(addToSet([
    { path: 'groups', type: ObjectIdSchema() },
    { path: 'orgs', type: ObjectIdSchema() },
    { path: 'aka', type: Type.String() }
  ])),
  $pull: Type.Optional(pull([
    { path: 'groups', type: ObjectIdSchema() },
    { path: 'orgs', type: ObjectIdSchema() },
    { path: 'aka', type: Type.String() }
  ]))
}, { additionalProperties: false })

export type PlansPatch = Static<typeof plansPatchSchema>
export const plansPatchValidator = getValidator(plansPatchSchema, dataValidator)
export const plansPatchResolver = resolve<PlansPatch, HookContext>({})

// Schema for allowed query properties - matching original schema
export const plansQuerySchema = Type.Object({
  ...querySyntax(plansSchema).properties
}, { additionalProperties: false })

export type PlansQuery = Static<typeof plansQuerySchema>
export const plansQueryValidator = getValidator(plansQuerySchema, queryValidator)
export const plansQueryResolver = resolve<PlansQuery, HookContext>({})
