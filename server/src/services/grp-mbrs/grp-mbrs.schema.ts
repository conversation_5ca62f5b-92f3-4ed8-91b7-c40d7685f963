// TypeBox schema for grp-mbrs service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

export const grpMbrsSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Required fields from original schema
  group: ObjectIdSchema(), // Required
  person: ObjectIdSchema(), // Required
  org: ObjectIdSchema(), // Required
  mbrId: Type.String(), // Required

  // Group member fields from original schema
  name: Type.Optional(Type.String()),
  email: Type.Optional(Type.String()),

  // Common fields
  ...commonFields.properties
}, { additionalProperties: false })

export type GrpMbrs = Static<typeof grpMbrsSchema>
export const grpMbrsValidator = getValidator(grpMbrsSchema, dataValidator)
export const grpMbrsResolver = resolve<GrpMbrs, HookContext>({})
export const grpMbrsExternalResolver = resolve<GrpMbrs, HookContext>({})

export const grpMbrsDataSchema = Type.Object({
  // No required fields for creation per original schema (inconsistent with main schema)
  ...Type.Partial(Type.Omit(grpMbrsSchema, ['_id'])).properties
}, { additionalProperties: false })

export type GrpMbrsData = Static<typeof grpMbrsDataSchema>
export const grpMbrsDataValidator = getValidator(grpMbrsDataSchema, dataValidator)
export const grpMbrsDataResolver = resolve<GrpMbrsData, HookContext>({})

export const grpMbrsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(grpMbrsSchema, ['_id'])).properties,

  // MongoDB operators
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any()))
}, { additionalProperties: false })
export type GrpMbrsPatch = Static<typeof grpMbrsPatchSchema>
export const grpMbrsPatchValidator = getValidator(grpMbrsPatchSchema, dataValidator)
export const grpMbrsPatchResolver = resolve<GrpMbrsPatch, HookContext>({})

// Schema for allowed query properties - matching original schema
export const grpMbrsQuerySchema = Type.Object({
  ...querySyntax(grpMbrsSchema).properties
}, { additionalProperties: false })

export type GrpMbrsQuery = Static<typeof grpMbrsQuerySchema>
export const grpMbrsQueryValidator = getValidator(grpMbrsQuerySchema, queryValidator)
export const grpMbrsQueryResolver = resolve<GrpMbrsQuery, HookContext>({})
