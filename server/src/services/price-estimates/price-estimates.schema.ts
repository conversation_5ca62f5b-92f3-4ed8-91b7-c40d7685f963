// TypeBox schema for price-estimates service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, ImageSchema, addToSet } from '../../utils/common/typebox-schemas.js'

// Simplified estimate body schema (would need full import in real implementation)
const EstimateBodySchema = Type.Object({
  // Simplified estimate body fields
  price: Type.Optional(Type.Number()),
  description: Type.Optional(Type.String())
}, { additionalProperties: true })

export const priceEstimatesSchema = Type.Object({
  _id: ObjectIdSchema(),

  // Required fields from original schema
  code: Type.String(), // Required
  locationCode: Type.String(), // Required

  // Estimate body fields spread (from estimateBodySchema.properties)
  price: Type.Optional(Type.Number()),
  description: Type.Optional(Type.String()),

  // Price estimate fields from original schema
  alts: Type.Optional(Type.Array(EstimateBodySchema)),
  rxcui: Type.Optional(Type.String()),
  listPrice: Type.Optional(Type.Number()),
  carrier: Type.Optional(Type.String()),
  files: Type.Optional(Type.Array(ImageSchema)),
  session: Type.Optional(Type.String()),
  zip_code: Type.Optional(Type.String()),
  state: Type.Optional(Type.String()),

  // Common fields
  ...commonFields.properties
}, { additionalProperties: false })

export type PriceEstimates = Static<typeof priceEstimatesSchema>
export const priceEstimatesValidator = getValidator(priceEstimatesSchema, dataValidator)
export const priceEstimatesResolver = resolve<PriceEstimates, HookContext>({})
export const priceEstimatesExternalResolver = resolve<PriceEstimates, HookContext>({})

export const priceEstimatesDataSchema = Type.Object({
  // Required fields for creating new price estimates
  code: Type.String(),
  locationCode: Type.String(),

  // All other fields from main schema (optional for creation)
  ...Type.Partial(Type.Omit(priceEstimatesSchema, ['_id', 'code', 'locationCode'])).properties
}, { additionalProperties: false })

export type PriceEstimatesData = Static<typeof priceEstimatesDataSchema>
export const priceEstimatesDataValidator = getValidator(priceEstimatesDataSchema, dataValidator)
export const priceEstimatesDataResolver = resolve<PriceEstimatesData, HookContext>({})

export const priceEstimatesPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(priceEstimatesSchema, ['_id'])).properties,

  // MongoDB operators - matching original pushPull for alts
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $addToSet: Type.Optional(addToSet([
    { path: 'alts', type: EstimateBodySchema }
  ])),
  $pull: Type.Optional(Type.Record(Type.String(), Type.Any()))
}, { additionalProperties: false })
export type PriceEstimatesPatch = Static<typeof priceEstimatesPatchSchema>
export const priceEstimatesPatchValidator = getValidator(priceEstimatesPatchSchema, dataValidator)
export const priceEstimatesPatchResolver = resolve<PriceEstimatesPatch, HookContext>({})

// Schema for allowed query properties - matching original schema
export const priceEstimatesQuerySchema = Type.Object({
  ...querySyntax(priceEstimatesSchema).properties
}, { additionalProperties: false })

export type PriceEstimatesQuery = Static<typeof priceEstimatesQuerySchema>
export const priceEstimatesQueryValidator = getValidator(priceEstimatesQuerySchema, queryValidator)
export const priceEstimatesQueryResolver = resolve<PriceEstimatesQuery, HookContext>({})

// Export for backward compatibility with business logic
export const estimateBodySchema = Type.Object({
  procedure: Type.Optional(Type.String()),
  provider: Type.Optional(Type.String()),
  location: Type.Optional(Type.String()),
  insurance: Type.Optional(Type.String())
}, { additionalProperties: false })

export const priceKeys = Type.Object({
  amount: Type.Optional(Type.Number()),
  copay: Type.Optional(Type.Number()),
  coinsurance: Type.Optional(Type.Number()),
  deductible: Type.Optional(Type.Number()),
  total: Type.Optional(Type.Number())
}, { additionalProperties: false })
